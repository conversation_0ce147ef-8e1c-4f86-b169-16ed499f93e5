
current_dir=$(shell pwd)
project_name=$(shell basename "${current_dir}")
work_dir=/data/jenkins_home/workspace

# 从环境变量获取参数，如果没有则使用默认值
REGISTRY ?= hub.skymind.io/merit-bo/
PROJECT_NAME ?= $(project_name)
IMAGE_TAG ?= latest

# 完整的镜像名称
FULL_IMAGE_NAME = $(REGISTRY)$(PROJECT_NAME):$(IMAGE_TAG)

image:
	@echo "Building image: $(FULL_IMAGE_NAME)"
	docker build -t $(FULL_IMAGE_NAME) .

push:
	@echo "Pushing image: $(FULL_IMAGE_NAME)"
	docker push $(FULL_IMAGE_NAME)

info:
	@echo "Registry: $(REGISTRY)"
	@echo "Project Name: $(PROJECT_NAME)"
	@echo "Image Tag: $(IMAGE_TAG)"
	@echo "Full Image Name: $(FULL_IMAGE_NAME)"

.PHONY: image push info
