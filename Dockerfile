# 第一阶段：构建阶段
FROM hub.skymind.io/library/node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 设置环境变量跳过 husky 安装
ENV HUSKY=0

# 安装所有依赖（包括开发依赖，因为构建需要）
RUN npm ci

# 复制源代码
COPY . .

# 构建项目（直接使用 vite build，跳过 tsc）
RUN npx vite build

# 第二阶段：生产阶段
FROM hub.skymind.io/library/nginx:1.23.0

# 删除默认的 nginx 静态文件
RUN rm -rf /usr/share/nginx/html/*

# 从构建阶段复制构建产物到 nginx 静态文件目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建一个简单的 nginx 配置来支持 SPA 路由
RUN echo 'server {\n\
    listen 80;\n\
    server_name localhost;\n\
    root /usr/share/nginx/html;\n\
    index index.html;\n\
    \n\
    # 支持 SPA 路由\n\
    location / {\n\
        try_files $uri $uri/ /index.html;\n\
    }\n\
    \n\
    # 静态资源缓存\n\
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n\
        expires 1y;\n\
        add_header Cache-Control \"public, immutable\";\n\
    }\n\
}' > /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]