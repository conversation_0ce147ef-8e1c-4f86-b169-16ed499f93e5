{"name": "tradex-web-bo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite", "lint:fix": "eslint . --ext ts,tsx --fix", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prepare": "husky install", "preview": "vite preview", "storybook": "storybook dev -p 6006 --no-open", "test": "vitest"}, "dependencies": {"@editorjs/code": "^2.9.3", "@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.3", "@editorjs/inline-code": "^1.5.2", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.8", "@editorjs/marker": "^1.4.0", "@editorjs/paragraph": "^2.11.7", "@editorjs/quote": "^2.7.6", "@editorjs/table": "^2.4.5", "@editorjs/underline": "^1.2.1", "@heroicons/react": "^2.1.3", "@hookform/resolvers": "^3.6.0", "@tanstack/react-query": "^5.45.0", "@tiptap/extension-character-count": "^3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-font-family": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-subscript": "^3.0.7", "@tiptap/extension-superscript": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/extension-youtube": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/downloadjs": "^1.4.6", "antd": "^5.22.2", "axios": "^1.7.2", "change-case": "^5.4.4", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "framer-motion": "^11.2.10", "html-react-parser": "^5.1.10", "immer": "^10.1.1", "js-base64": "^3.7.7", "js-file-download": "^0.4.12", "jsencrypt": "^3.3.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "numeral": "^2.0.6", "qs": "^6.13.0", "quill": "^2.0.3", "rc-picker": "^4.6.3", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.51.5", "react-indiana-drag-scroll": "^2.2.0", "react-pdf": "^9.0.0", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-router-dom": "^6.23.1", "react-select": "^5.8.0", "react-svg": "^16.1.34", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "recoil": "^0.7.7", "tailwind-merge": "^2.3.0", "usehooks-ts": "^3.1.0", "yup": "^1.4.0"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@storybook/addon-essentials": "^8.1.9", "@storybook/addon-links": "^8.1.9", "@storybook/addon-styling-webpack": "^1.0.0", "@storybook/builder-vite": "^8.1.9", "@storybook/react-vite": "^8.1.9", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tanstack/eslint-plugin-query": "^5.43.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/lodash": "^4.14.202", "@types/node": "^20.14.2", "@types/numeral": "^2.0.5", "@types/qs": "^6.9.15", "@types/quill": "^2.0.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@vercel/style-guide": "^6.0.0", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "autoprefixer": "^10.4.19", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-storybook": "^0.8.0", "husky": "^9.0.11", "jsdom": "^24.1.0", "lint-staged": "^15.2.7", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.4", "react-vite": "^0.0.5", "sass": "^1.77.5", "storybook": "^8.1.9", "storybook-addon-react-router-v6": "^2.0.15", "storybook-dark-mode": "^4.0.1", "tailwindcss": "^3.4.4", "typescript": "^5.4.5", "vite": "^5.3.0", "vite-plugin-static-copy": "^1.0.6", "vitest": "^1.6.0"}}