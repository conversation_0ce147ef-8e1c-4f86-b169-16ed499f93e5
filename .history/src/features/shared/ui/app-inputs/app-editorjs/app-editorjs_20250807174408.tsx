import { forwardRef, useEffect, useImperativeHandle, useRef, useCallback } from 'react'
import EditorJS, { OutputData } from '@editorjs/editorjs'
import Header from '@editorjs/header'
import Paragraph from '@editorjs/paragraph'
import List from '@editorjs/list'
import Link from '@editorjs/link'
import Image from '@editorjs/image'
import Table from '@editorjs/table'
import Quote from '@editorjs/quote'
import Code from '@editorjs/code'
import Underline from '@editorjs/underline'
import Marker from '@editorjs/marker'
import InlineCode from '@editorjs/inline-code'
import { ReactSVG } from 'react-svg'
import { formInvalidIcon } from '../../app-icons'
import './app-editorjs.css'

interface AppEditorJSProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  error?: string
  wrapperClassName?: string
  required?: boolean
  disabled?: boolean
  maxLength?: number
}

export interface AppEditorJSRef {
  focus: () => void
  blur: () => void
  getEditor: () => EditorJS | null
}

function AppEditorJSInner(
  props: AppEditorJSProps,
  ref: React.ForwardedRef<AppEditorJSRef>
) {
  const {
    label,
    value = '',
    onChange,
    onBlur,
    placeholder = 'Enter article content...',
    error,
    wrapperClassName,
    required,
    disabled,
    maxLength = 10000
  } = props

  const editorRef = useRef<EditorJS | null>(null)
  const holderRef = useRef<HTMLDivElement>(null)
  const isInitialized = useRef(false)
  const characterCount = useRef(0)

  // Convert HTML to Editor.js data format
  const htmlToEditorData = useCallback((html: string): OutputData => {
    if (!html || html.trim() === '') {
      return {
        time: Date.now(),
        blocks: [],
        version: '2.30.8'
      }
    }

    const blocks: any[] = []

    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // Convert common HTML elements to Editor.js blocks
    const elements = tempDiv.children

    for (let i = 0; i < elements.length; i++) {
      const element = elements[i]
      const tagName = element.tagName.toLowerCase()

      if (tagName === 'p') {
        const text = element.innerHTML.trim()
        if (text) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'paragraph',
            data: {
              text: text
            }
          })
        }
      } else if (tagName.match(/^h[1-6]$/)) {
        const level = parseInt(tagName.charAt(1))
        const text = element.textContent?.trim() || ''
        if (text) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'header',
            data: {
              text: text,
              level: level
            }
          })
        }
      } else if (tagName === 'ul' || tagName === 'ol') {
        const items = Array.from(element.children)
          .map(li => li.innerHTML?.trim() || '')
          .filter(item => item)
        if (items.length > 0) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'list',
            data: {
              style: tagName === 'ul' ? 'unordered' : 'ordered',
              items: items
            }
          })
        }
      } else if (tagName === 'blockquote') {
        const text = element.textContent?.trim() || ''
        if (text) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'quote',
            data: {
              text: text,
              caption: ''
            }
          })
        }
      } else if (tagName === 'pre') {
        const codeElement = element.querySelector('code')
        const code = codeElement ? codeElement.textContent : element.textContent
        if (code?.trim()) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'code',
            data: {
              code: code.trim()
            }
          })
        }
      } else if (tagName === 'table') {
        const rows = Array.from(element.querySelectorAll('tr'))
        const content = rows.map(row =>
          Array.from(row.querySelectorAll('td, th')).map(cell => cell.textContent?.trim() || '')
        ).filter(row => row.length > 0)

        if (content.length > 0) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'table',
            data: {
              withHeadings: element.querySelector('th') !== null,
              content: content
            }
          })
        }
      } else {
        // Fallback to paragraph for unknown elements
        const text = element.innerHTML?.trim() || element.textContent?.trim() || ''
        if (text) {
          blocks.push({
            id: `block_${Date.now()}_${i}`,
            type: 'paragraph',
            data: {
              text: text
            }
          })
        }
      }
    }

    // If no blocks were created but there's content, create a paragraph with the raw HTML
    if (blocks.length === 0 && html.trim() !== '') {
      blocks.push({
        id: `block_${Date.now()}_0`,
        type: 'paragraph',
        data: {
          text: html.trim()
        }
      })
    }

    return {
      time: Date.now(),
      blocks: blocks,
      version: '2.30.8'
    }
  }, [])

  // Convert Editor.js data to HTML
  const editorDataToHtml = useCallback((data: OutputData): string => {
    if (!data || !data.blocks || data.blocks.length === 0) {
      return ''
    }

    return data.blocks.map(block => {
      switch (block.type) {
        case 'paragraph':
          return `<p>${block.data.text || ''}</p>`
        case 'header':
          const level = block.data.level || 1
          return `<h${level}>${block.data.text || ''}</h${level}>`
        case 'list':
          const tag = block.data.style === 'ordered' ? 'ol' : 'ul'
          const items = (block.data.items || []).map((item: string) => `<li>${item}</li>`).join('')
          return `<${tag}>${items}</${tag}>`
        case 'quote':
          return `<blockquote>${block.data.text || ''}</blockquote>`
        case 'code':
          return `<pre><code>${block.data.code || ''}</code></pre>`
        case 'table':
          if (block.data.content && Array.isArray(block.data.content)) {
            const rows = block.data.content.map((row: string[]) => {
              const cells = row.map(cell => `<td>${cell}</td>`).join('')
              return `<tr>${cells}</tr>`
            }).join('')
            return `<table>${rows}</table>`
          }
          return ''
        default:
          return `<p>${block.data.text || ''}</p>`
      }
    }).join('')
  }, [])

  // Initialize Editor.js
  const initializeEditor = useCallback(async () => {
    if (!holderRef.current || isInitialized.current || disabled) {
      return
    }

    try {
      const initialData = htmlToEditorData(value)
      
      const editor = new EditorJS({
        holder: holderRef.current,
        data: initialData,
        placeholder: placeholder,
        readOnly: disabled,
        tools: {
          header: {
            class: Header,
            config: {
              levels: [1, 2, 3, 4, 5, 6],
              defaultLevel: 2
            }
          },
          paragraph: {
            class: Paragraph,
            inlineToolbar: true
          },
          list: {
            class: List,
            inlineToolbar: true
          },
          quote: {
            class: Quote,
            inlineToolbar: true
          },
          code: Code,
          table: {
            class: Table,
            inlineToolbar: true
          },
          link: {
            class: Link,
            config: {
              endpoint: '/api/link-preview' // You might need to implement this endpoint
            }
          },
          image: {
            class: Image,
            config: {
              endpoints: {
                byFile: '/api/upload-image', // You might need to implement this endpoint
                byUrl: '/api/upload-image-by-url'
              }
            }
          },
          underline: Underline,
          marker: Marker,
          inlineCode: InlineCode
        },
        onChange: async (api) => {
          try {
            const outputData = await api.saver.save()
            const htmlContent = editorDataToHtml(outputData)
            
            // Update character count
            const textContent = outputData.blocks
              .map(block => block.data.text || block.data.code || '')
              .join(' ')
            characterCount.current = textContent.length
            
            onChange?.(htmlContent)
          } catch (error) {
            console.error('Error saving editor data:', error)
          }
        },
        onReady: () => {
          isInitialized.current = true
        }
      })

      editorRef.current = editor
    } catch (error) {
      console.error('Error initializing Editor.js:', error)
    }
  }, [value, placeholder, disabled, onChange, htmlToEditorData, editorDataToHtml])

  // Cleanup editor
  const destroyEditor = useCallback(() => {
    if (editorRef.current && isInitialized.current) {
      try {
        editorRef.current.destroy()
        editorRef.current = null
        isInitialized.current = false
      } catch (error) {
        console.error('Error destroying editor:', error)
      }
    }
  }, [])

  useImperativeHandle(ref, () => ({
    focus: () => {
      // Editor.js doesn't have a direct focus method, but we can focus the holder
      if (holderRef.current) {
        holderRef.current.focus()
      }
    },
    blur: () => {
      onBlur?.()
    },
    getEditor: () => editorRef.current
  }))

  // Initialize editor on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      initializeEditor()
    }, 100) // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(timer)
      destroyEditor()
    }
  }, [initializeEditor, destroyEditor])

  // Update editor content when value changes externally
  useEffect(() => {
    if (editorRef.current && isInitialized.current && value !== undefined) {
      const currentData = htmlToEditorData(value)
      editorRef.current.render(currentData).catch(error => {
        console.error('Error updating editor content:', error)
      })
    }
  }, [value, htmlToEditorData])

  const isOverLimit = maxLength && characterCount.current > maxLength

  return (
    <div className={wrapperClassName}>
      <label className="block text-xs font-semibold capitalize not-italic text-txt-title">
        {label}
        {required && (
          <span className="ml-1 inline-block text-xs font-semibold capitalize not-italic text-txt-negative">
            *
          </span>
        )}
      </label>
      
      <div className="mt-2">
        <div 
          className={`editorjs-editor ${error ? 'error' : ''} ${disabled ? 'disabled' : ''} ${isOverLimit ? 'over-limit' : ''}`}
        >
          <div 
            ref={holderRef}
            className="editorjs-content"
            style={{ minHeight: '200px' }}
          />
        </div>
        
        {/* Character count and error info */}
        <div className="mt-1 flex justify-between items-center">
          <div>
            {error && (
              <p className="flex items-center text-xs normal-case text-txt-negative">
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-negative')
                  }}
                  className="mr-1"
                  src={formInvalidIcon}
                />
                {error}
              </p>
            )}
          </div>
          <span className={`text-xs ${isOverLimit ? 'text-txt-negative' : 'text-txt-inactive'}`}>
            {characterCount.current}/{maxLength}
          </span>
        </div>
      </div>
    </div>
  )
}

export const AppEditorJS = forwardRef(AppEditorJSInner)
