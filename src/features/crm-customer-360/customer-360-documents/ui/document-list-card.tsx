import { ReactSVG } from 'react-svg'
import { reportsIcon, trashFilledIcon } from '@/features/shared/ui/app-icons'
import { AppButton } from '@/features/shared/ui'

interface SalesOpportunityCardProps {
  title: string
  onClick: () => void
  onDelete: () => void
}

export function DocumentListCard(props: SalesOpportunityCardProps) {
  const { title, onClick, onDelete } = props
  return (
    <AppButton
      className="w-full p-0 text-left"
      cosmeticType="transparent"
      onClick={onClick}
    >
      <section className="flex w-full items-center justify-between gap-1 py-3">
        <div className="flex items-center gap-1">
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-title')
            }}
            src={reportsIcon}
          />
          <div className="flex flex-col gap-1">
            <div className="flex gap-1">
              <p className="leading-[14px]">{title}</p>
            </div>
          </div>
        </div>
        <ReactSVG
          onClick={(e) => {
            e.stopPropagation()
            onDelete()
          }}
          src={trashFilledIcon}
        />
      </section>
    </AppButton>
  )
}
