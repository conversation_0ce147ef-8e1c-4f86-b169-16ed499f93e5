import { useState, useMemo } from 'react'
import { ReactSVG } from 'react-svg'
import { useQuery } from '@tanstack/react-query'
import { pdfjs, Document, Page } from 'react-pdf'
import { AppModal, AppButton } from '@/features/shared/ui'
import { useDownload } from '@/features/shared/utils'
import { CommonServices, type DocumentItem } from '@/features/shared/services'
import { downloadIcon } from '@/features/shared/ui/app-icons'
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

interface ViewModalProps {
  selectedRecord: DocumentItem | undefined
  onClose: () => void
}

export function DocumentViewModal(props: ViewModalProps) {
  const { selectedRecord, onClose } = props
  const { downloadFile } = useDownload()
  const [numPages, setNumPages] = useState(0)

  const existingFileKey = selectedRecord?.fileKey
  const existingFileName = selectedRecord?.fileName
  const existingFileType = selectedRecord?.fileType.split('/').pop()

  const { data: existingFile, isLoading: isLoadingExistingFile } = useQuery({
    enabled: Boolean(existingFileKey),
    queryFn: () => CommonServices.getFile(existingFileKey!),
    queryKey: ['getFile', existingFileKey],
  })

  const existingFileURL = useMemo(
    () => (existingFile ? URL.createObjectURL(existingFile) : ''),
    [existingFile],
  )

  const handleDownload = (
    file: File | Blob,
    fileName: string,
    fileType?: string,
  ) => {
    if (!fileType) return
    downloadFile({
      file,
      fileExtension: `.${fileType}`,
      filename: fileName,
    })
  }

  return (
    <>
      {selectedRecord ? (
        <AppModal
          childrenWrapperClassName="pb-0" // required for sticky footer
          onCancel={onClose}
          open
          titleLabel="View Document"
          width={790}
        >
          <div className="mb-1 text-txt-label">{selectedRecord.fileName}</div>
          <div className="flex h-[600px] w-full items-center justify-center rounded bg-white">
            {existingFile && existingFileType ? (
              <>
                {['png', 'jpg', 'jpeg'].includes(existingFileType) ? (
                  <img
                    alt="thumbnail"
                    className="max-h-full max-w-full object-contain"
                    src={existingFileURL}
                  />
                ) : (
                  <Document
                    file={existingFileURL}
                    onLoadSuccess={({ numPages }) => {
                      setNumPages(numPages)
                    }}
                  >
                    <div className="flex max-h-[600px] flex-col items-center overflow-y-auto overflow-x-hidden">
                      {[...Array(numPages)].map((_, index) => (
                        <Page
                          key={`upload_page_${index + 1}`}
                          pageNumber={index + 1}
                          width={742}
                        />
                      ))}
                    </div>
                  </Document>
                )}
              </>
            ) : null}

            {/* Loading State */}
            {isLoadingExistingFile ? (
              <span className="font-light text-txt-inactive">
                Loading File...
              </span>
            ) : null}
          </div>

          <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white pb-6 pt-3">
            <AppButton
              cosmeticType="skeleton"
              label="Cancel"
              onClick={onClose}
            />
            {existingFile && existingFileName ? (
              <AppButton
                cosmeticType="secondary"
                label="Download"
                onClick={() => {
                  handleDownload(
                    existingFile,
                    existingFileName,
                    existingFileType,
                  )
                }}
                prefixIcon={
                  <ReactSVG
                    beforeInjection={(svg) => {
                      svg.classList.add('fill-txt-inverted')
                    }}
                    src={downloadIcon}
                  />
                }
              />
            ) : null}
          </section>
        </AppModal>
      ) : null}
    </>
  )
}
