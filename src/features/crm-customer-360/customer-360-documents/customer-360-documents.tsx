import { useRef, useMemo, useState } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { AppButton, AppInfoContainerCollapse } from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { loaderIcon, plusIcon } from '@/features/shared/ui/app-icons'
import {
  CommonServices,
  CRMLeadServices,
  type DocumentItem,
} from '@/features/shared/services'
import { DocumentListCard, DocumentViewModal } from './ui'

const ADD_OPPORTUNITY_PERMISSION = 1600500 // TODO: Align once BE adds

export function Customer360Documents() {
  const { id: leadId } = useParams()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { hasPermission } = usePermission()
  const queryClient = useQueryClient()
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<
    DocumentItem | undefined
  >(undefined)

  const { data: documentData } = useQuery({
    queryFn: () =>
      CRMLeadServices.getDocumentList({
        leadId: Number(leadId),
      }),
    queryKey: ['document-list-by-id', leadId],
  })
  const documentList = useMemo(
    () => documentData?.documentList ?? [],
    [documentData],
  )

  const onChangeFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files === null) return
    const file = e.target.files[0]
    if (!file) return
    try {
      const response = await uploadFile.mutateAsync(file)
      try {
        await uploadDocument.mutateAsync({
          action: 'ADD',
          fileKey: response.dosKey,
          fileName: file.name,
          fileType: file.type,
          leadId: Number(leadId),
        })
      } catch (error) {
        console.error(error)
      }
    } catch (error) {
      console.error(error)
    } finally {
      e.target.value = ''
    }
  }

  const uploadFile = useMutation({
    mutationFn: CommonServices.uploadFile,
  })

  const uploadDocument = useMutation({
    mutationFn: CRMLeadServices.uploadDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['document-list-by-id'],
      })
    },
  })

  const deleteDocument = (item: DocumentItem) => {
    uploadDocument.mutateAsync({
      action: 'DELETE',
      ...item,
    })
  }

  return (
    <>
      <AppInfoContainerCollapse
        containerClassName="space-y-2"
        headerChildren="Documents"
      >
        <div className="flex flex-col items-center gap-2">
          {/* Loading State */}
          {uploadDocument.isPending ||
            (uploadFile.isPending && (
              <ReactSVG className="animate-spin" src={loaderIcon} />
            ))}

          {/* Empty State */}
          {!uploadDocument.isPending &&
            !uploadFile.isPending &&
            documentList.length === 0 && (
              <div className="py-8 text-center text-sm font-light text-txt-inactive">
                No Documents Available
              </div>
            )}

          {/* Show State */}
          {(!uploadDocument.isPending || !uploadFile.isPending) &&
            documentList.length > 0 &&
            documentList.map((item: DocumentItem) => (
              <DocumentListCard
                key={item.documentId}
                onClick={() => {
                  setShowViewModal(true)
                  setSelectedRecord(item)
                }}
                onDelete={() => {
                  deleteDocument(item)
                }}
                title={item.fileName}
              />
            ))}

          {hasPermission(ADD_OPPORTUNITY_PERMISSION) && (
            <>
              <AppButton
                className="min-w-0 text-txt-label"
                cosmeticType="transparent"
                disabled={uploadDocument.isPending || uploadFile.isPending}
                label="Add"
                onClick={() => {
                  fileInputRef.current?.click()
                }}
                suffixIcon={
                  <ReactSVG
                    beforeInjection={(svg) => {
                      svg.classList.add('fill-txt-label', 'w-3', 'h-3')
                    }}
                    src={plusIcon}
                  />
                }
              />
              <input
                accept="image/*"
                className="hidden"
                onChange={onChangeFile}
                ref={fileInputRef}
                type="file"
              />
            </>
          )}
        </div>
      </AppInfoContainerCollapse>
      {showViewModal ? (
        <DocumentViewModal
          onClose={() => {
            setShowViewModal(false)
          }}
          selectedRecord={selectedRecord}
        />
      ) : null}
    </>
  )
}
