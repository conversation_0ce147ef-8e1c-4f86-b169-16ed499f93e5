import { Drawer } from 'antd'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { ChatServices } from '@/features/shared/services'
import { AppChatThread, AppChatEmptyPlaceholder } from '@/features/shared/ui'
import { Chat<PERSON>rawerFooter, ChatDrawerHeader } from './ui'

const CURRENT_TIME = dayjs().valueOf()
const QUERY_REFETCH_INTERVAL = 5000

interface Customer360ChatDrawerProps {
  customerId: number
  isOpened: boolean
  onClose: () => void
}

export function Customer360ChatDrawer(props: Customer360ChatDrawerProps) {
  const { customerId, isOpened, onClose } = props

  const [referenceTime, setReferenceTime] = useState(CURRENT_TIME)

  const { data, isLoading } = useQuery({
    enabled: Boolean(customerId),
    placeholderData: keepPreviousData,
    queryFn: () =>
      ChatServices.getChatMessages({
        customerId,
        direction: 'BEFORE',
        limit: 20,
        referenceTime,
      }),
    queryKey: ['chat-messages-by-id', customerId, referenceTime],
    refetchInterval: QUERY_REFETCH_INTERVAL,
  })
  const messageList = data?.messageList ?? []

  const onSendSuccess = () => {
    setReferenceTime(dayjs().valueOf())
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setReferenceTime(dayjs().valueOf())
    }, QUERY_REFETCH_INTERVAL)

    return () => {
      clearInterval(interval)
    } // Clear interval on component unmount
  }, [])

  return (
    <div className="absolute right-0 top-0 z-50 h-full">
      <div className="fixed h-[calc(100vh-92px)]">
        {isOpened ? (
          <Drawer
            className="rounded"
            classNames={{
              body: 'bg-bg-default flex flex-col h-full !pb-0 gap-2',
            }}
            closable={false}
            getContainer={false}
            onClose={onClose}
            open={isOpened}
            placement="right"
            styles={{
              wrapper: {
                width: 444,
              },
            }}
          >
            <ChatDrawerHeader onClose={onClose} />

            <div className="max-h-full grow overflow-y-auto">
              {messageList.length > 0 ? (
                <div className="flex flex-col gap-1">
                  {messageList.map((message, index) => (
                    <AppChatThread
                      data={message}
                      isFirstMessage={
                        index > 0
                          ? messageList[index - 1]?.senderId !==
                            message.senderId
                          : true
                      }
                      key={index}
                    />
                  ))}
                </div>
              ) : null}

              {messageList.length === 0 && !isLoading && (
                <AppChatEmptyPlaceholder />
              )}
            </div>

            <ChatDrawerFooter
              customerId={customerId}
              onSendSuccess={onSendSuccess}
            />
          </Drawer>
        ) : null}
      </div>
    </div>
  )
}
