import { useQueryClient, useMutation } from '@tanstack/react-query'
import {
  ChatServices,
  type ChatMessageAttachment,
} from '@/features/shared/services'
import { MeritDivider } from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import {
  ChatComposer,
  type ChatComposerFormData,
} from '@/features/shared/widgets'

const QUERY_WAIT_TIME = 1000

interface ChatDrawerFooterProps {
  customerId: number
  onSendSuccess: () => void
}

export function ChatDrawerFooter(props: ChatDrawerFooterProps) {
  const { customerId, onSendSuccess } = props
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const mutateSend = useMutation({
    mutationFn: ChatServices.sendChatMessage,
    onError: () => {
      showToast(
        'Failed to send message',
        'Message could not be sent. Please try again.',
        'danger',
      )
    },
    onSuccess: () => {
      showToast('Message Sent Successfully', '', 'success')
      setTimeout(() => {
        onSendSuccess()
        queryClient.invalidateQueries({
          queryKey: ['chat-messages-by-id'],
        })
      }, QUERY_WAIT_TIME)
    },
  })

  const handleSend = (data: ChatComposerFormData) => {
    if (data.message?.length || data.attachments?.length) {
      const payload = {
        customerId,
        fileList: (data.attachments || []) as ChatMessageAttachment[],
        message: data.message || '',
      }
      mutateSend.mutateAsync(payload)
    }
  }

  return (
    <section className="sticky bottom-0 left-0 right-0 w-full bg-bg-default pb-4">
      <MeritDivider />
      <ChatComposer
        maxLength={500}
        onSend={handleSend}
        placeholder="Type message ..."
        warningLength={400}
      />
    </section>
  )
}
