import { ReactSVG } from 'react-svg'
import { App<PERSON><PERSON><PERSON>, MeritDivider } from '@/features/shared/ui'
import {
  chatIcon,
  txButtonNavigationIcon,
} from '@/features/shared/ui/app-icons'

interface ChatDrawerHeaderProps {
  onClose: () => void
}

export function ChatDrawerHeader(props: ChatDrawerHeaderProps) {
  const { onClose } = props

  return (
    <div className="space-y-1">
      <section className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title', 'w-3.5', 'h-3.5')
            }}
            src={chatIcon}
          />
          <h2 className="text-sm font-semibold leading-[14px]">Chat</h2>
        </div>

        <div className="flex items-center gap-1.5">
          <span className="text-xs leading-3 text-txt-paragraph">Close</span>
          <AppButton
            className="p-1.5"
            onClick={onClose}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add(
                    'fill-bg-button-neutral-still-pale',
                    'w-4',
                    'h-4',
                  )
                }}
                src={txButtonNavigationIcon}
              />
            }
          />
        </div>
      </section>
      <MeritDivider />
    </div>
  )
}
