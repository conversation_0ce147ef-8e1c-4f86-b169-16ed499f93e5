import { type Customer360SummaryType } from '@/features/shared/services'
import { AppButton } from '@/features/shared/ui'

interface SettingMenuProps {
  onSelect: (summaryType: Customer360SummaryType) => void
}

export function SettingMenu(props: SettingMenuProps) {
  const { onSelect } = props
  return (
    <div className="flex flex-col">
      <AppButton
        className="justify-start"
        cosmeticType="transparent"
        label="By Allocation Class"
        onClick={() => {
          onSelect('ALLOCATION_CLASS')
        }}
      />
      <AppButton
        className="justify-start"
        cosmeticType="transparent"
        label="By Asset"
        onClick={() => {
          onSelect('ASSET')
        }}
      />
      <AppButton
        className="justify-start"
        cosmeticType="transparent"
        label="By Risk Rating"
        onClick={() => {
          onSelect('RISK_RATING')
        }}
      />
      <AppButton
        className="justify-start"
        cosmeticType="transparent"
        label="By Risk Level"
        onClick={() => {
          onSelect('RISK_LEVEL')
        }}
      />
    </div>
  )
}
