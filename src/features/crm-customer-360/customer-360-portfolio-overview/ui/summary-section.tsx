import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { AppInfoContainerPopover } from '@/features/shared/ui'
import {
  type Customer360SummaryType,
  type Customer360AllocationKeys,
  type Customer360RiskRatingKeys,
  type Customer360RiskLevelKeys,
  CRMLeadServices,
  type LeadPortfolioSummaryItem,
} from '@/features/shared/services'
import {
  Customer360AllocationClasses,
  Customer360RiskLevels,
  Customer360RiskRatings,
} from '@/features/shared/mappings'
import { AppInfoCardAssetChanges } from '@/features/shared/ui/app-info-cards/app-info-card-asset-changes'
import { SettingMenu } from './setting-menu'
import { SettingMenuLabel } from './setting-menu-label'

const getRiskBadgeStyles = (item: LeadPortfolioSummaryItem) => {
  switch (item.riskTag) {
    case 'LOW':
      return 'bg-risk-level-low text-txt-risk-level-low'
    case 'MEDIUM':
      return 'bg-risk-level-medium text-txt-risk-level-medium'
    case 'HIGH':
      return 'bg-risk-level-high text-txt-risk-level-high'
    default:
      return 'default'
  }
}

interface SummarySectionProps {
  customerId?: number
}

export function SummarySection(props: SummarySectionProps) {
  const { customerId } = props
  const [summaryBy, setSummaryBy] =
    useState<Customer360SummaryType>('ALLOCATION_CLASS')

  const { data } = useQuery({
    enabled: Boolean(customerId),
    queryFn: () =>
      CRMLeadServices.getLeadPortfolioSummary({
        customerId: Number(customerId),
        summaryBy,
      }),
    queryKey: ['lead-portfolio-summary', customerId, summaryBy],
  })

  const onSelectSetting = (summaryType: Customer360SummaryType) => {
    setSummaryBy(summaryType)
  }

  return (
    <AppInfoContainerPopover
      popoverChildren={<SettingMenu onSelect={onSelectSetting} />}
      title="Summary"
      titleChildren={<SettingMenuLabel summaryBy={summaryBy} />}
    >
      {summaryBy === 'ALLOCATION_CLASS' &&
        data?.summaryList.map((item) => (
          <AppInfoCardAssetChanges
            amount={item.marketValue}
            badge={item.riskTag}
            badgeType="primary"
            changeAmount={item.unrealizedGl}
            changePercentage={item.unrealizedGlRate}
            currency={item.currency}
            key={item.summaryName}
            label={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360AllocationClasses[
                item.summaryName as Customer360AllocationKeys
              ]
                ? Customer360AllocationClasses[
                    item.summaryName as Customer360AllocationKeys
                  ].label
                : item.summaryName
            }
            labelIndicatorColor={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360AllocationClasses[
                item.summaryName as Customer360AllocationKeys
              ]
                ? Customer360AllocationClasses[
                    item.summaryName as Customer360AllocationKeys
                  ].color
                : 'rgb(var(--color-chart-light-red))'
            }
            percent={item.percentage}
          />
        ))}

      {summaryBy === 'ASSET' &&
        data?.summaryList.map((item) => (
          <AppInfoCardAssetChanges
            amount={item.marketValue}
            badge={item.riskTag}
            badgeClassName={getRiskBadgeStyles(item)}
            changeAmount={item.unrealizedGl}
            changePercentage={item.unrealizedGlRate}
            currency={item.currency}
            key={item.summaryName}
            label={item.summaryName}
            logo={item.assetList[0]?.logo}
            percent={item.percentage}
            showLogo
          />
        ))}

      {summaryBy === 'RISK_LEVEL' &&
        data?.summaryList.map((item) => (
          <AppInfoCardAssetChanges
            amount={item.marketValue}
            changeAmount={item.unrealizedGl}
            changePercentage={item.unrealizedGlRate}
            currency={item.currency}
            key={item.summaryName}
            label={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360RiskLevels[
                item.summaryName as Customer360RiskLevelKeys
              ]
                ? Customer360RiskLevels[
                    item.summaryName as Customer360RiskLevelKeys
                  ].label
                : item.summaryName
            }
            labelIndicatorColor={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360RiskLevels[
                item.summaryName as Customer360RiskLevelKeys
              ]
                ? Customer360RiskLevels[
                    item.summaryName as Customer360RiskLevelKeys
                  ].color
                : 'rgb(var(--color-chart-light-red))'
            }
            percent={item.percentage}
          />
        ))}

      {summaryBy === 'RISK_RATING' &&
        data?.summaryList.map((item) => (
          <AppInfoCardAssetChanges
            amount={item.marketValue}
            badge={item.riskTag}
            badgeClassName={getRiskBadgeStyles(item)}
            changeAmount={item.unrealizedGl}
            changePercentage={item.unrealizedGlRate}
            currency={item.currency}
            key={item.summaryName}
            label={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360RiskRatings[
                item.summaryName as Customer360RiskRatingKeys
              ]
                ? Customer360RiskRatings[
                    item.summaryName as Customer360RiskRatingKeys
                  ].label
                : item.summaryName
            }
            labelIndicatorColor={
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              Customer360RiskRatings[
                item.summaryName as Customer360RiskRatingKeys
              ]
                ? Customer360RiskRatings[
                    item.summaryName as Customer360RiskRatingKeys
                  ].color
                : 'rgb(var(--color-chart-light-red))'
            }
            percent={item.percentage}
          />
        ))}
    </AppInfoContainerPopover>
  )
}
