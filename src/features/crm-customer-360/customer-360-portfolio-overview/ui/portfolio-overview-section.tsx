import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  CRMLeadServices,
  type LeadPortfolioPeriod,
} from '@/features/shared/services'
import {
  AppAreaChart,
  AppInfoContainerPopover,
  AppTimeFrameFilter,
} from '@/features/shared/ui'
import { generateAreaChartDataPoints } from '@/features/shared/utils'
import { useChart } from '../utils'

const TIMEFRAMES: LeadPortfolioPeriod[] = ['1W', '1M', '1Y', '3Y', '5Y', 'YTD']

interface PortfolioOverviewSectionProps {
  customerId?: number
}

export function PortfolioOverviewSection(props: PortfolioOverviewSectionProps) {
  const { customerId } = props
  const { getPortfolioKeyValuePairs, getDataKeys } = useChart()
  const [period, setPeriod] = useState<LeadPortfolioPeriod>('1W')

  const { data: historyData } = useQuery({
    enabled: Boolean(customerId),
    queryFn: () =>
      CRMLeadServices.getLeadPortfolioHistory({
        customerId: Number(customerId),
        period,
      }),
    queryKey: ['lead-portfolio-statistics', customerId, period],
  })
  const portfolioDataPoints = useMemo(
    () => getPortfolioKeyValuePairs(historyData?.list ?? []),
    [getPortfolioKeyValuePairs, historyData?.list],
  )
  const chartDataKeys = useMemo(
    () =>
      getDataKeys(
        ['rgb(var(--color-chart-emerald-green))'],
        [{ instrumentId: 'portfolio', name: 'Your Portfolio' }],
      ),
    [getDataKeys],
  )
  const chartData = generateAreaChartDataPoints(
    [portfolioDataPoints],
    chartDataKeys,
    'instrumentId',
    false,
  )

  const onChangeTimeframe = (period: string) => {
    setPeriod(period as LeadPortfolioPeriod)
  }

  return (
    <AppInfoContainerPopover hasSettings={false} title="Portfolio Overview">
      <div className="space-y-2">
        <AppAreaChart
          className="pb-0"
          dataKeys={chartDataKeys}
          dataPoints={chartData}
          gradient={{
            bottomIsClear: true,
            bottomOpacity: 0,
            topOffset: 0,
            topOpacity: 0,
          }}
          showLegend={false}
          timeStampFormat={
            ['3Y', '5Y', 'YTD'].includes(period) ? 'DD-MM-YYYY' : undefined
          }
        />

        <AppTimeFrameFilter
          onChange={onChangeTimeframe}
          selectedTimeframe="1W"
          timeframes={TIMEFRAMES}
        />
      </div>
    </AppInfoContainerPopover>
  )
}
