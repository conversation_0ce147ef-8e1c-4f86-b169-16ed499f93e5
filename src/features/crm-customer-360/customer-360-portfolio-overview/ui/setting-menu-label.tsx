import { type Customer360SummaryType } from '@/features/shared/services'
import { Customer360SummaryTypes } from '@/features/shared/mappings'

interface SettingMenuLabelProps {
  summaryBy: Customer360SummaryType
}

export function SettingMenuLabel(props: SettingMenuLabelProps) {
  const { summaryBy } = props

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const label = Customer360SummaryTypes[summaryBy]?.label ?? ''

  return (
    <div className="grow text-sm leading-4 text-txt-paragraph">By {label}</div>
  )
}
