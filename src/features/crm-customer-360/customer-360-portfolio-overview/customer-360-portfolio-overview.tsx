import { MeritDivider } from '@/features/shared/ui'
import { PortfolioOverviewSection, SummarySection } from './ui'

interface Customer360PortfolioOverviewProps {
  customerId?: number
}

export function Customer360PortfolioOverview(
  props: Customer360PortfolioOverviewProps,
) {
  const { customerId } = props
  return (
    <div className="rounded-sm bg-bg-default px-3 py-2">
      <PortfolioOverviewSection customerId={customerId} />
      <MeritDivider />
      <SummarySection customerId={customerId} />
    </div>
  )
}
