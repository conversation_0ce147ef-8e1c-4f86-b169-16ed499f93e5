import {
  type LeadPortfolioChartItem,
  // type MarketBenchmarkDataResponse,
  type LeadPortfolioDataItem,
} from '@/features/shared/services'

export const useChart = () => {
  /**
   * @param data - \{ "d": "2024-10-21", "v": "-276.79", "r": "-0.29" \}[]
   * @returns - \{ key: d, value: v \}[]
   */
  const getKeyValuePairs = (data: LeadPortfolioDataItem[]): DataPoint[] => {
    return data.map((item) => ({
      key: item.d,
      value: parseFloat(item.b),
    }))
  }

  // Function to map colors
  const getDataKeys = (
    colors: string[],
    data: LeadPortfolioChartItem[],
  ): DataModel[] => {
    return data.map((item, index) => ({
      color: colors[index] ?? 'rgb(var(--color-chart-blue))',
      label: item.name || 'Benchmark',
      value: item.instrumentId || 'benchmark',
    }))
  }

  const getPortfolioKeyValuePairs = (
    data: LeadPortfolioDataItem[],
  ): ChartDataItem => {
    return {
      data: getKeyValuePairs(data),
      instrumentId: 'portfolio',
    } as unknown as Omit<ChartDataItem, 'data'> & { data: DataPoint[] }
  }

  return {
    getDataKeys,
    getKeyValuePairs,
    getPortfolioKeyValuePairs,
  }
}
