import { ReactSVG } from 'react-svg'
import { AppBreadcrumb } from '@/features/shared/ui'
import { lightBulbIcon, usersIcon } from '@/features/shared/ui/app-icons'

export function Customer360Breadcrumbs() {
  return (
    <div className="mb-4">
      <AppBreadcrumb
        breadcrumbs={[
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-paragraph', 'w-3.5', 'h-3.5')
                }}
                src={usersIcon} // TODO: Update
              />
            ),
            label: 'Customer Relationship Management',
          },
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-paragraph', 'w-3.5', 'h-3.5')
                }}
                src={lightBulbIcon}
              />
            ),
            label: 'Customer Dashboard',
          },
        ]}
      />
    </div>
  )
}
