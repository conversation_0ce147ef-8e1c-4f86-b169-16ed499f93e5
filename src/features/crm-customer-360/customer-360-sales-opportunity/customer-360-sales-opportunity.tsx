import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import dayjs from 'dayjs'
import { AppButton, AppInfoContainerCollapse } from '@/features/shared/ui'
import {
  CRMLeadServices,
  type LeadSalesOpportunity,
} from '@/features/shared/services'
import { usePermission } from '@/features/shared/utils'
import { plusIcon } from '@/features/shared/ui/app-icons'
import { Customer360SalesOpportunityEditModal } from '../customer-360-sales-opportunity-edit-modal'
import { SalesOpportunityCard, SalesOpportunityHeader } from './ui'

const ADD_OPPORTUNITY_PERMISSION = 1600500 // TODO: Align once BE adds

export function Customer360SalesOpportunity() {
  const { id: leadId } = useParams()
  const { hasPermission } = usePermission()
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedOpportunity, setSelectedOpportunity] =
    useState<LeadSalesOpportunity>()

  const { data: leadData } = useQuery({
    queryFn: () =>
      CRMLeadServices.getLeadDetail({
        leadId: Number(leadId),
      }),
    queryKey: ['lead-detail-by-id', leadId],
  })

  const { data: salesOpportunityData } = useQuery({
    queryFn: () =>
      CRMLeadServices.getLeadSalesOpportunityList({
        dateTimeFrom: 0,
        dateTimeTo: 0,
        keyword: '',
        leadId: Number(leadId),
        pageNum: 1,
        pageSize: 20,
        searchType: 'SUBJECT',
        sortBy: '',
        sortType: '',
        statusList: ['NEW', 'QUALIFIED', 'PROPOSAL_SENT', 'IN_NEGOTIATION'],
      }),
    queryKey: ['sales-opportunity-by-lead-id', leadId],
  })
  const salesOpportunityList = useMemo(
    () => salesOpportunityData?.salesOpportunityList ?? [],
    [salesOpportunityData],
  )

  const handleAddOpportunity = () => {
    setSelectedOpportunity(undefined)
    setShowEditModal(true)
  }

  const handleViewOpportunity = (opportunity: LeadSalesOpportunity) => {
    setSelectedOpportunity(opportunity)
    setShowEditModal(true)
  }

  return (
    <>
      <AppInfoContainerCollapse
        containerClassName="space-y-2"
        headerChildren={
          <SalesOpportunityHeader count={salesOpportunityList.length} />
        }
      >
        <div className="flex flex-col items-center gap-2">
          {salesOpportunityList.length > 0 ? (
            salesOpportunityList.map((item) => (
              <SalesOpportunityCard
                key={item.salesOpportunityId}
                onClick={() => {
                  handleViewOpportunity(item)
                }}
                status={item.status}
                timestamp={item.dateTime}
                title={item.subject}
              />
            ))
          ) : (
            <div className="py-8 text-center text-sm font-light text-txt-inactive">
              No Sales Opportunities Available
            </div>
          )}

          {hasPermission(ADD_OPPORTUNITY_PERMISSION) && (
            <AppButton
              className="min-w-0 text-txt-label"
              cosmeticType="transparent"
              label="Add"
              onClick={handleAddOpportunity}
              suffixIcon={
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('fill-txt-label', 'w-3', 'h-3')
                  }}
                  src={plusIcon}
                />
              }
            />
          )}
        </div>
      </AppInfoContainerCollapse>
      {showEditModal ? (
        <Customer360SalesOpportunityEditModal
          initialValues={{
            date: selectedOpportunity?.dateTime
              ? dayjs(selectedOpportunity.dateTime).format('DD-MM-YYYY')
              : '',
            details: selectedOpportunity?.description ?? '',
            leadId: leadData?.leadId,
            salesId: selectedOpportunity?.salesOpportunityId ?? 0,
            status: selectedOpportunity?.status ?? '',
            subject: selectedOpportunity?.subject ?? '',
            time: selectedOpportunity?.dateTime
              ? dayjs(selectedOpportunity.dateTime).format('HH:mm')
              : '',
          }}
          leadIsVip={leadData?.isVip}
          leadName={leadData?.name ?? ''}
          mode={selectedOpportunity ? 'EDIT' : 'ADD'}
          onClose={() => {
            setSelectedOpportunity(undefined)
            setShowEditModal(false)
          }}
        />
      ) : null}
    </>
  )
}
