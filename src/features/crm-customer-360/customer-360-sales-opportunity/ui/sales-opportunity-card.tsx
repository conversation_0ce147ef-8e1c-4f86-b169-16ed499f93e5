import { ReactSVG } from 'react-svg'
import dayjs from 'dayjs'
import { reportsIcon } from '@/features/shared/ui/app-icons'
import { AppButton, AppTag } from '@/features/shared/ui'
import { LeadSalesOpportunityStatuses } from '@/features/shared/mappings'
import { type LeadSalesOpportunityStatusType } from '@/features/shared/services'

interface SalesOpportunityCardProps {
  title: string
  status: string
  timestamp?: number
  onClick: () => void
}

export function SalesOpportunityCard(props: SalesOpportunityCardProps) {
  const { title, status, timestamp, onClick } = props
  return (
    <AppButton
      className="w-full p-0 text-left"
      cosmeticType="transparent"
      onClick={onClick}
    >
      <section className="flex w-full gap-1">
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-title')
          }}
          src={reportsIcon}
        />
        <div className="flex flex-col gap-1">
          <div className="flex gap-1">
            <p className="leading-[14px]">{title}</p>{' '}
            <AppTag>
              {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
                LeadSalesOpportunityStatuses[
                  status as LeadSalesOpportunityStatusType
                ]
                  ? LeadSalesOpportunityStatuses[
                      status as LeadSalesOpportunityStatusType
                    ].label
                  : status
              }
            </AppTag>
          </div>
          {timestamp ? (
            <span className="text-xs font-light text-txt-paragraph">
              {dayjs(timestamp).format('DD-MM-YYYY HH:mm') || '-'}
            </span>
          ) : null}
        </div>
      </section>
    </AppButton>
  )
}
