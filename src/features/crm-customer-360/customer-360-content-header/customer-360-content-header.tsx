import { useNavigate, useParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { useQuery } from '@tanstack/react-query'
import { AppButton, ContentHeader } from '@/features/shared/ui'
import { chatIcon, plusIcon } from '@/features/shared/ui/app-icons'
import { usePermission } from '@/features/shared/utils'
import {
  type ChatListSearchType,
  ChatServices,
} from '@/features/shared/services'

const ASSIGN_RM_PERMISSION = 1600100
const CHAT_PERMISSION = 1600600

const QUERY_REFETCH_INTERVAL = 8000

interface Customer360ContentHeaderProps {
  title?: string
  onAssignRM: () => void
  onOpenChat: () => void
}

export function Customer360ContentHeader(props: Customer360ContentHeaderProps) {
  const { onAssignRM, onOpenChat } = props
  const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const { id: leadId } = useParams()

  const { data: chatData } = useQuery({
    enabled: Boolean(leadId),
    queryFn: () =>
      ChatServices.getChatList({
        keyword: leadId ?? '',
        searchType: 'CUSTOMER_ID' as ChatListSearchType,
      }),
    queryKey: ['chat-list', leadId],
    refetchInterval: QUERY_REFETCH_INTERVAL,
  })
  const hasUnreadMessage =
    (chatData?.chatList || [])[0]?.unreadMessageCount ?? 0

  const handleBack = () => {
    navigate('/admin-panel/crm/customer-relations/list')
  }

  return (
    <ContentHeader onBack={handleBack} title="Customer 360">
      {hasPermission(ASSIGN_RM_PERMISSION) && (
        <AppButton
          className="min-w-[120px]"
          label="Assign/Transfer RM"
          onClick={onAssignRM}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={plusIcon}
            />
          }
        />
      )}
      {hasPermission(CHAT_PERMISSION) && (
        <AppButton
          badgeOptions={{
            dot: true,
            style: {
              backgroundColor: 'rgba(var(--color-btn-negative))',
              color: 'white',
            },
          }}
          cosmeticType={hasUnreadMessage ? 'tertiary' : 'quaternary'}
          hasBadge={Boolean(hasUnreadMessage)}
          onClick={onOpenChat}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={chatIcon}
            />
          }
        />
      )}
    </ContentHeader>
  )
}
