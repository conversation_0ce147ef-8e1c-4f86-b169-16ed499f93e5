import { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { AppInfoContainerCollapse } from '@/features/shared/ui'
import { TaskServices } from '@/features/shared/services'
import { Customer360TaskCard, Customer360TasksHeader } from './ui'

export function Customer360Tasks() {
  const { id: leadId } = useParams()

  const { data: taskData } = useQuery({
    enabled: Boolean(leadId),
    queryFn: () =>
      TaskServices.getTaskList({
        endDateTimeFrom: 0,
        endDateTimeTo: 0,
        isReturnActive: true,
        keyword: '',
        leadId: Number(leadId),
        pageNum: 1,
        pageSize: 10,
        salesOpportunityStatus: '',
        searchType: '',
        sortBy: 'DESC',
        sortType: 'CREATE_TIME',
        startDateTimeFrom: 0,
        startDateTimeTo: 0,
        taskType: 'CUSTOMER_TASK',
        userIds: [],
      }),
    queryKey: ['customer-360-task-list', leadId],
  })
  const taskList = useMemo(() => taskData?.taskList ?? [], [taskData])

  return (
    <AppInfoContainerCollapse
      containerClassName="space-y-2"
      headerChildren={<Customer360TasksHeader count={taskList.length} />}
    >
      <div className="flex flex-col items-center gap-2">
        {taskList.length > 0 ? (
          taskList.map((item) => (
            <Customer360TaskCard
              isOverdue={item.isOverdue}
              key={item.taskId}
              status={item.status}
              timestamp={item.endDateTime}
              title={item.title}
            />
          ))
        ) : (
          <div className="py-8 text-center text-sm font-light text-txt-inactive">
            No Tasks/Reminders Available
          </div>
        )}
      </div>
    </AppInfoContainerCollapse>
  )
}
