import { ReactSVG } from 'react-svg'
import dayjs from 'dayjs'
import { reportsIcon } from '@/features/shared/ui/app-icons'
import { AppTag } from '@/features/shared/ui'
import { TASK_STATUSES } from '@/features/shared/mappings'
import { type TaskStatus } from '@/features/shared/services'
import { cn } from '@/features/shared/utils'

interface Customer360TaskCardProps {
  title: string
  status: string
  isOverdue?: boolean
  timestamp?: number
}

export function Customer360TaskCard(props: Customer360TaskCardProps) {
  const { title, status, isOverdue, timestamp } = props
  return (
    <section
      className={cn(
        'flex w-full gap-1 rounded-sm bg-bg-tone p-2',
        isOverdue && 'bg-bg-negative',
      )}
    >
      <ReactSVG
        beforeInjection={(svg) => {
          svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-title')
        }}
        src={reportsIcon}
      />
      <div className="flex flex-col gap-1">
        <div className="flex gap-1">
          <p className="leading-[14px]">{title}</p>{' '}
          <AppTag variant="success">
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              TASK_STATUSES[status as TaskStatus]
                ? TASK_STATUSES[status as TaskStatus].label
                : status
            }
          </AppTag>
        </div>
        {timestamp ? (
          <span className="text-xs font-light text-txt-paragraph">
            {dayjs(timestamp).format('DD-MM-YYYY HH:mm') || '-'}
          </span>
        ) : null}
      </div>
    </section>
  )
}
