import { ReactSVG } from 'react-svg'
import { medalStarIcon, userIcon } from '@/features/shared/ui/app-icons'

interface CustomerDetailHeaderProps {
  isVip?: boolean
  userName: string
}

export function CustomerDetailHeader(props: CustomerDetailHeaderProps) {
  const { userName = '', isVip = false } = props
  return (
    <div className="flex items-center gap-1">
      <ReactSVG
        beforeInjection={(svg) => {
          svg.classList.add('w-6', 'h-6', 'mr-1')
        }}
        src={userIcon}
      />
      <span className="text-xs font-medium text-txt-title">{userName}</span>
      {isVip ? <ReactSVG src={medalStarIcon} /> : null}
    </div>
  )
}
