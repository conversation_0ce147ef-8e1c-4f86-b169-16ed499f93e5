import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { useSetRecoilState } from 'recoil'
import {
  AppButton,
  AppInfoCardVariant9,
  AppInfoContainerCollapse,
  AppTag,
} from '@/features/shared/ui'
import { editIcon } from '@/features/shared/ui/app-icons'
import { CRMLeadServices, type LeadPriority } from '@/features/shared/services'
import { LeadPriorityLevels } from '@/features/shared/mappings'
import { customerDetailsState } from '@/features/shared/states'
import { usePermission } from '@/features/shared/utils'
import { Customer360CustomerDetailEditModal } from '../customer-360-customer-detail-edit-modal'
import { CustomerDetailHeader } from './ui'

const EDIT_LEAD_PERMISSION = 1600500
const VIEW_CUSTOMER_LIST_PERMISSION = 400050

export function Customer360CustomerDetail() {
  const [showEditModal, setShowEditModal] = useState(false)
  const { id: leadId } = useParams()
  const navigate = useNavigate()
  const setCustomerDetailState = useSetRecoilState(customerDetailsState)
  const { hasPermission } = usePermission()

  const { data: leadData } = useQuery({
    queryFn: () =>
      CRMLeadServices.getLeadDetail({
        leadId: Number(leadId),
      }),
    queryKey: ['lead-detail-by-id', leadId],
  })

  const handleEditCustomer = () => {
    setShowEditModal(true)
  }
  const handleViewCustomerDetail = () => {
    setCustomerDetailState({
      activeModalScreen: 'information-overview',
      customerId: leadData?.customerId,
      isPasswordConfirmationOpened: false,
    })
    navigate('/admin-panel/customer-management/list')
  }

  return (
    <>
      <AppInfoContainerCollapse
        containerClassName="space-y-2"
        headerChildren={
          <CustomerDetailHeader
            isVip={leadData?.isVip}
            userName={leadData?.name ?? ''}
          />
        }
      >
        <div>
          <AppInfoCardVariant9
            contents={[
              {
                label: 'Assigned RM',
                value: leadData?.assignedRm,
              },
              {
                label: 'Email Address',
                value: leadData?.emailAddress,
              },
              {
                label: 'Phone Number',
                value: leadData?.phoneNumber,
              },
              {
                label: 'Customer ID',
                value: leadData?.customerId,
              },
              {
                label: 'Birthday',
                value: leadData?.birthday,
              },
              {
                label: 'Country/Region',
                value: leadData?.countryRegion,
              },
              {
                label: 'Priority',
                value: leadData?.priority,
                valueColor: leadData?.priority
                  ? LeadPriorityLevels[
                      leadData.priority.toUpperCase() as LeadPriority
                    ].color
                  : '',
              },
              {
                label: 'Type',
                value: leadData?.type,
              },
            ]}
          />
          <div>
            <div className="my-1.5 min-w-28 text-xs font-light not-italic leading-[13.8px] text-txt-paragraph">
              Tags
            </div>
            <div className="flex flex-wrap gap-1">
              {leadData?.tagList?.map((tag, index) => (
                <AppTag className="py-1 pe-2 ps-2" key={index}>
                  {tag}
                </AppTag>
              ))}
            </div>
          </div>

          {hasPermission(EDIT_LEAD_PERMISSION) && (
            <AppButton
              className="mx-auto text-txt-label underline"
              cosmeticType="transparent"
              label="Edit"
              onClick={handleEditCustomer}
              suffixIcon={
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('fill-txt-label', 'w-3', 'h-3')
                  }}
                  src={editIcon}
                />
              }
            />
          )}
        </div>

        {leadData?.customerId &&
        hasPermission(VIEW_CUSTOMER_LIST_PERMISSION) ? (
          <AppButton
            className="w-full"
            label="View Customer Detail"
            onClick={handleViewCustomerDetail}
          />
        ) : null}
      </AppInfoContainerCollapse>
      {showEditModal ? (
        <Customer360CustomerDetailEditModal
          initialValues={{
            address: leadData?.address ?? '',
            birthday: leadData?.birthday
              ? dayjs(leadData.birthday).valueOf()
              : undefined,
            countryRegion: leadData?.countryRegion ?? '',
            customerId: leadData?.customerId ?? undefined,
            emailAddress: leadData?.emailAddress ?? '',
            isVip: leadData?.isVip ?? false,
            leadId: leadData?.leadId ?? 0,
            name: leadData?.name ?? '',
            phoneNumber: leadData?.phoneNumber ?? '',
            priority: leadData?.priority.toUpperCase() ?? '',
            status: leadData?.status ?? '',
            tags: leadData?.tagList?.map((tag) => ({ label: tag })) ?? [],
            type: leadData?.type ?? '',
          }}
          onClose={() => {
            setShowEditModal(false)
          }}
        />
      ) : null}
    </>
  )
}
