import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { AppButton, AppInfoContainerVariant1 } from '@/features/shared/ui'
import { CRMLeadServices, type LeadNote } from '@/features/shared/services'
import { plusIcon } from '@/features/shared/ui/app-icons'
import { usePermission } from '@/features/shared/utils'
import { Customer360InternalNotesDetailModal } from '../customer-360-internal-notes-detail-modal'
import { NoteCard } from './ui'

const ADD_NOTE_PERMISSION = 1600200

export function Customer360InternalNotes() {
  const { hasPermission } = usePermission()
  const [selectedNote, setSelectedNote] = useState<LeadNote>()
  const [showEditModal, setShowEditModal] = useState(false)
  const { id: leadId } = useParams()

  const { data: leadNoteData } = useQuery({
    enabled: Boolean(leadId),
    queryFn: () =>
      CRMLeadServices.getLeadNoteList({
        leadId: Number(leadId),
      }),
    queryKey: ['lead-note-list', leadId],
  })
  const noteList = leadNoteData?.results ?? []

  const handleAddNote = () => {
    setSelectedNote(undefined)
    setShowEditModal(true)
  }

  const handleViewNote = (note: LeadNote) => {
    setSelectedNote(note)
    setShowEditModal(true)
  }

  return (
    <>
      <AppInfoContainerVariant1
        containerClassName="h-full"
        hasMeritDivider
        menuChildren={
          hasPermission(ADD_NOTE_PERMISSION) ? (
            <AppButton
              className="min-w-0 p-0 text-txt-paragraph"
              cosmeticType="transparent"
              label="Add"
              onClick={handleAddNote}
              prefixIcon={
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('fill-txt-paragraph', 'w-3', 'h-3')
                  }}
                  src={plusIcon}
                />
              }
            />
          ) : undefined
        }
        title="Internal Notes"
      >
        {noteList.length ? (
          <div className="grid gap-1">
            {noteList.map((note) => (
              <NoteCard
                hasAttachment={Boolean(note.fileList?.length)}
                key={note.id}
                onClick={() => {
                  handleViewNote(note)
                }}
                preview={note.details}
                title={note.title}
                updatedTime={note.updatedAt}
              />
            ))}
          </div>
        ) : (
          <div className="py-8 text-center text-sm font-light text-txt-inactive">
            No Internal Notes Available
          </div>
        )}
      </AppInfoContainerVariant1>

      {showEditModal ? (
        <Customer360InternalNotesDetailModal
          initialValues={{
            details: selectedNote?.details ?? '',
            fileList:
              selectedNote?.fileList?.map((item) => ({
                fileKey: item.fileKey,
                fileName: item.fileName,
                fileType: item.fileType,
              })) ?? [],
            noteId: selectedNote?.id ?? 0,
            title: selectedNote?.title ?? '',
          }}
          leadId={Number(leadId)}
          mode={selectedNote ? 'EDIT' : 'ADD'}
          onClose={() => {
            setSelectedNote(undefined)
            setShowEditModal(false)
          }}
        />
      ) : null}
    </>
  )
}
