import { ReactSVG } from 'react-svg'
import dayjs from 'dayjs'
import { clipIcon } from '@/features/shared/ui/app-icons'
import { AppButton } from '@/features/shared/ui'

interface NoteCardProps {
  hasAttachment?: boolean
  preview: string
  title: string
  updatedTime: number
  onClick: () => void
}

export function NoteCard(props: NoteCardProps) {
  const { preview, hasAttachment = false, title, updatedTime, onClick } = props

  return (
    <AppButton
      className="p-0 text-left"
      cosmeticType="transparent"
      onClick={onClick}
    >
      <article className="w-full space-y-1 rounded-sm bg-bg-tone p-2">
        <div className="flex items-center justify-between">
          <h4 className="text-xs leading-3 text-txt-title">{title}</h4>
          {hasAttachment ? (
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title', 'w-3.5', 'h-3.5')
              }}
              src={clipIcon}
            />
          ) : null}
        </div>

        <div>
          <p className="line-clamp-3 text-xs font-light leading-[15px] text-txt-paragraph">
            {preview}
          </p>
        </div>

        <div className="text-right text-[10px] font-light text-txt-inactive">
          {updatedTime ? dayjs(updatedTime).format('DD-MM-YYYY HH:mm') : ''}
        </div>
      </article>
    </AppButton>
  )
}
