import { ReactSVG } from 'react-svg'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import * as yup from 'yup'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMemo, useState } from 'react'
import {
  AppButton,
  AppInfoCardVariant9,
  AppModal,
  AppNormalInput,
  MeritDivider,
} from '@/features/shared/ui'
import {
  doubleArrowRightIcon,
  magnifyingGlassIcon,
} from '@/features/shared/ui/app-icons'
import {
  CRMLeadServices,
  RMServices,
  type RmTreeChildItem,
  type RmTreeItem,
} from '@/features/shared/services'
import { useToast } from '@/features/shared/utils'
import { ManagerList } from './ui'

const schema = yup.object().shape({
  rmSearchQuery: yup.string(),
})
type FormValues = yup.InferType<typeof schema>

interface Customer360AssignRMModalProps {
  onClose: () => void
}
export function Customer360AssignRMModal(props: Customer360AssignRMModalProps) {
  const { onClose } = props
  const { id: leadId } = useParams()
  const [selectedManager, setSelectedManager] = useState<RmTreeItem>()
  const [selectedRM, setSelectedRM] = useState<RmTreeChildItem>()
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const { register, reset, watch } = useForm<FormValues>({
    defaultValues: {
      rmSearchQuery: '',
    },
    resolver: yupResolver(schema),
  })
  const watchedRmSearchQuery = watch('rmSearchQuery')

  const { data: leadData } = useQuery({
    enabled: Boolean(leadId),
    queryFn: () =>
      CRMLeadServices.getLeadDetail({
        leadId: Number(leadId),
      }),
    queryKey: ['lead-detail-by-id', leadId],
  })

  const { data: rmTreeData } = useQuery({
    enabled: watchedRmSearchQuery !== '',
    queryFn: () =>
      RMServices.getRmTree({
        keyword: watchedRmSearchQuery ?? '',
        position: 'MANAGER',
      }),
    queryKey: ['rm-manager-tree-search', watchedRmSearchQuery],
  })
  const rmList = useMemo(() => selectedManager?.ChildList, [selectedManager])

  const mutateAssignRM = useMutation({
    mutationFn: CRMLeadServices.assignRMToLead,
    onError: (error) => {
      showToast('Lead Details Update Unsuccessful', error.message, 'danger')
    },
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['lead-detail-by-id'],
      })
      if (selectedRM) {
        showToast(
          'RM Assigned To A Client',
          `You have assigned ${selectedRM.name} to a client ${leadData?.name}`,
          'success',
        )
      } else {
        showToast(
          'RM Assigned To A Client',
          `You have assigned yourself to a client - ${leadData?.name}`,
          'success',
        )
      }
    },
  })

  const __onClose = () => {
    reset({})
    onClose()
  }
  const onAssignToMe = () => {
    if (leadId) {
      const payload = {
        isAssignToMe: true,
        leadId: Number(leadId),
      }
      mutateAssignRM.mutateAsync(payload)
    }
  }
  const onAssignRM = () => {
    if (leadId && selectedRM) {
      const payload = {
        isAssignToMe: false,
        leadId: Number(leadId),
        memberId: selectedRM.id,
      }
      mutateAssignRM.mutateAsync(payload)
    }
  }
  const onSelectManager = (manager: RmTreeItem | RmTreeChildItem) => {
    setSelectedManager(manager as RmTreeItem)
  }
  const onSelectRM = (rm: RmTreeChildItem) => {
    setSelectedRM(rm)
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleLabel="Assign/Transfer RM"
      width={600}
    >
      <div className="flex flex-col gap-3">
        <AppInfoCardVariant9
          cols={2}
          containerClassName="mb-1"
          contents={[
            { label: 'Lead ID', value: leadId },
            { label: 'Lead Name', value: leadData?.name },
          ]}
        />

        <div className="flex items-center bg-bg-accent">
          <span className="pl-3 pr-1">
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title', 'w-4', 'h-4')
              }}
              src={magnifyingGlassIcon}
            />
          </span>
          <AppNormalInput
            {...register('rmSearchQuery')}
            inputWrapperClassName="mt-0"
            placeholder="Search Manager ..."
            wrapperClassName="grow"
          />
        </div>

        <MeritDivider />

        <section className="grid grid-cols-[1fr_auto_1fr] items-center gap-1.5">
          <ManagerList
            data={rmTreeData?.data ?? []}
            onSelect={onSelectManager}
            selectedId={selectedManager?.id}
            title="Managers"
          />
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title', 'w-4', 'h-4')
            }}
            src={doubleArrowRightIcon}
          />
          <ManagerList
            data={rmList}
            onSelect={onSelectRM}
            selectedId={selectedRM?.id}
            title="RMs"
          />
        </section>

        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-3 gap-2 bg-white py-6">
          <AppButton
            cosmeticType="skeleton"
            label="Cancel"
            onClick={__onClose}
          />

          <AppButton
            isLoading={mutateAssignRM.isPending}
            label="Assign To Me"
            onClick={onAssignToMe}
          />

          <AppButton
            cosmeticType="secondary"
            disabled={!selectedRM}
            isLoading={mutateAssignRM.isPending}
            label="Assign"
            onClick={onAssignRM}
          />
        </section>
      </div>
    </AppModal>
  )
}
