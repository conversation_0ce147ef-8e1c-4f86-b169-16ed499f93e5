import { AppButton } from '@/features/shared/ui'

interface ManagerListItemProps {
  name: string
  isSelected?: boolean
  onSelect: () => void
}

export function ManagerListItem(props: ManagerListItemProps) {
  const { name, isSelected, onSelect } = props
  return (
    <AppButton
      className="justify-start font-light"
      cosmeticType={isSelected ? 'tertiary' : 'transparent'}
      label={name}
      onClick={onSelect}
    />
  )
}
