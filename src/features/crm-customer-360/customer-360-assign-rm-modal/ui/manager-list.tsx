import {
  type RmTreeChildItem,
  type RmTreeItem,
} from '@/features/shared/services'
import { ManagerListItem } from './manager-list-item'

interface ManagerListProps {
  data?: RmTreeItem[] | RmTreeChildItem[]
  selectedId?: number
  title: string
  onSelect: (manager: RmTreeItem | RmTreeChildItem) => void
}

export function ManagerList(props: ManagerListProps) {
  const { data, title, onSelect, selectedId } = props
  return (
    <div className="relative flex h-[298px] flex-col gap-1 overflow-scroll bg-bg-tone px-2 pb-2">
      <div className="sticky left-0 right-0 top-0 bg-bg-tone px-3 pt-2 text-[10px] text-txt-paragraph">
        {title}
      </div>
      {data?.length ? (
        data.map((manager) => (
          <ManagerListItem
            isSelected={manager.id === selectedId}
            key={manager.id}
            name={manager.name}
            onSelect={() => {
              onSelect(manager)
            }}
          />
        ))
      ) : (
        <div className="px-3 py-2.5 text-xs font-light text-txt-inactive">
          Select Manager
        </div>
      )}
    </div>
  )
}
