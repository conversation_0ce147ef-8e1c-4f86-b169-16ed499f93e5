import { ReactSVG } from 'react-svg'
import { AppButton } from '@/features/shared/ui'
import { downloadIcon, trashIcon } from '@/features/shared/ui/app-icons'

interface FileItemProps {
  filename: string
  disableDownload?: boolean
  onDelete: () => void
  onDownload: () => void
}

export function FileItem(props: FileItemProps) {
  const { filename, disableDownload, onDelete, onDownload } = props

  return (
    <div className="flex items-center justify-between gap-2">
      <div className="text-xs font-light text-txt-paragraph">{filename}</div>
      <div className="flex items-center gap-1">
        {!disableDownload && (
          <AppButton
            className="min-w-0 p-0"
            cosmeticType="transparent"
            onClick={onDownload}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title', 'w-3.5', 'h-3.5')
                }}
                src={downloadIcon}
              />
            }
          />
        )}

        <AppButton
          className="min-w-0 p-0"
          cosmeticType="transparent"
          onClick={onDelete}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title', 'w-3.5', 'h-3.5')
              }}
              src={trashIcon}
            />
          }
        />
      </div>
    </div>
  )
}
