import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useFieldArray, useForm } from 'react-hook-form'
import { ReactSVG } from 'react-svg'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  AppButton,
  AppFileUploadInput,
  AppModal,
  AppNormalInput,
  AppNormalTextarea,
  RequiredIndicator,
} from '@/features/shared/ui'
import {
  cn,
  useDownload,
  usePermission,
  useToast,
} from '@/features/shared/utils'
import {
  arrowClockwiseIcon,
  informationIcon,
} from '@/features/shared/ui/app-icons'
import {
  CommonServices,
  CRMLeadServices,
  type LeadNoteActionType,
} from '@/features/shared/services'
import { FileItem } from './ui'

const DELETE_PERMISSION = 1600400
const EDIT_PERMISSION = 1600300

const schema = yup
  .object()
  .shape({
    details: yup.string().required('Details is a required field'),
    fileList: yup
      .array()
      .of(
        yup
          .object()
          .shape({
            fileKey: yup.string().required(),
            fileName: yup.string().required(),
            fileType: yup.string().required(),
          })
          .required(),
      )
      .required(),
    leadId: yup.number(),
    noteId: yup.number(),
    title: yup.string().required('Title is a required field'),
  })
  .required()

export type FormValues = yup.InferType<typeof schema>

interface Customer360InternalNotesDetailModalProps {
  initialValues: FormValues
  leadId: number
  mode: 'ADD' | 'EDIT'
  onClose: () => void
}
export function Customer360InternalNotesDetailModal(
  props: Customer360InternalNotesDetailModalProps,
) {
  const { mode, onClose, initialValues, leadId } = props
  const { hasPermission } = usePermission()
  const { downloadFile } = useDownload()
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const {
    control,
    register,
    reset,
    handleSubmit,
    setValue,
    formState: { isValid, errors },
    watch,
  } = useForm<FormValues>({
    defaultValues: {
      ...initialValues,
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })
  const watchedFileList = watch('fileList')
  const watchedNoteId = watch('noteId')
  const watchedTitle = watch('title')

  const { append, remove } = useFieldArray({
    control,
    name: 'fileList',
  })

  const mutateGetFile = useMutation({
    mutationFn: CommonServices.getFile,
    onError: () => {
      showToast(
        `Download Failed`,
        `Unsuccessful while downloading the file. Please try again later.`,
        'danger',
      )
    },
  })

  const mutateAddNote = useMutation({
    mutationFn: CRMLeadServices.addLeadNote,
    onError: () => {
      showToast(
        'Adding Note Failed',
        'Unsuccessful while adding the new note. Please try again later.',
        'danger',
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['lead-note-list'],
      })
      showToast(
        'Note Added Successfully',
        `You have added a new note - ${watchedTitle} - to the lead`,
        'success',
      )
      __onClose()
    },
  })

  const mutateDeleteNote = useMutation({
    mutationFn: CRMLeadServices.deleteLeadNote,
    onError: () => {
      showToast(
        'Note Deletion Failed',
        `Unsuccessful while deleting the note - ${watchedTitle}`,
        'danger',
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['lead-note-list'],
      })
      showToast(
        'Note Deleted Successfully',
        `You have deleted the note - ${watchedTitle}`,
        'success',
      )
      __onClose()
    },
  })

  const mutateEditNote = useMutation({
    mutationFn: CRMLeadServices.editLeadNote,
    onError: () => {
      showToast(
        'Note Update Failed',
        `Unsuccessful while updating the note - ${watchedTitle}`,
        'danger',
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['lead-note-list'],
      })
      showToast(
        'Note Updated Successfully',
        `You have updated the note - ${watchedTitle}`,
        'success',
      )
      __onClose()
    },
  })

  const __onClose = () => {
    reset({})
    onClose()
  }

  const __onSubmit = (data: FormValues) => {
    if (mode === 'ADD') {
      const payload = {
        action: 'ADD' as LeadNoteActionType,
        details: data.details,
        fileList: data.fileList,
        leadId,
        title: data.title,
      }
      mutateAddNote.mutateAsync(payload)
    } else if (data.noteId) {
      const payload = {
        action: 'EDIT' as LeadNoteActionType,
        details: data.details,
        fileList: data.fileList,
        noteId: data.noteId,
        title: data.title,
      }
      mutateEditNote.mutateAsync(payload)
    }
  }

  const onClearDetails = () => {
    setValue('details', '', {
      shouldDirty: true,
      shouldTouch: true,
    })
  }

  const onDeleteNote = () => {
    if (watchedNoteId) {
      const payload = {
        action: 'DELETE' as LeadNoteActionType,
        noteId: watchedNoteId,
      }
      mutateDeleteNote.mutateAsync(payload)
      __onClose()
    }
  }

  const onDeleteFile = (index: number) => {
    remove(index)
  }
  const onDownloadFile = async (
    fileKey: string,
    fileName: string,
    fileType?: string,
  ) => {
    if (!fileType) return
    const file = await mutateGetFile.mutateAsync(fileKey)
    downloadFile({
      file,
      fileExtension: `.${fileType}`,
      filename: fileName,
    })
  }
  const onUploadFile = (fileKey: string, filename: string) => {
    const nameChunks = filename.split('.')
    const fileName = nameChunks.slice(0, -1).join('.')
    const fileType = nameChunks[nameChunks.length - 1]
    if (fileName && fileType) {
      append({ fileKey, fileName, fileType })
    }
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleLabel={mode === 'ADD' ? 'Add Note' : 'View/Edit Note'}
      width={600}
    >
      <form className="flex flex-col gap-3" onSubmit={handleSubmit(__onSubmit)}>
        <AppNormalInput
          label="Title"
          {...register('title')}
          error={errors.title?.message}
          placeholder="Input Title"
          required
        />

        <section>
          <div className="flex items-center justify-between">
            <label
              className="block text-xs font-semibold capitalize not-italic text-txt-title"
              htmlFor="fileListUpload"
            >
              Details
              <span className="ml-1 inline-block text-xs font-semibold capitalize not-italic text-txt-negative">
                *
              </span>
            </label>

            <AppButton
              className="min-w-0 p-0 text-xs font-light"
              cosmeticType="transparent"
              label="Clear"
              onClick={onClearDetails}
              prefixIcon={
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('fill-txt-title', 'w-3', 'h-3')
                  }}
                  src={arrowClockwiseIcon}
                />
              }
            />
          </div>
          <AppNormalTextarea
            wrapperClassName="-mt-0.5"
            {...register('details')}
            error={errors.details?.message}
            placeholder="Input Details"
            required
          />
        </section>

        <section className="flex flex-col gap-2">
          <AppFileUploadInput
            id="fileListUpload"
            label="Upload Document"
            maxFileSize={30}
            onChange={(fileKey, file) => {
              if (!file) return
              onUploadFile(fileKey, file.name)
            }}
            placeholder="Upload document"
          />
          <div className="flex flex-col gap-1">
            {watchedFileList.map((item, index) => (
              <FileItem
                disableDownload={!item.fileType}
                filename={`${item.fileName}.${item.fileType}`}
                key={item.fileKey}
                onDelete={() => {
                  onDeleteFile(index)
                }}
                onDownload={() => {
                  onDownloadFile(item.fileKey, item.fileName, item.fileType)
                }}
              />
            ))}
          </div>
          <div className="flex items-center gap-1">
            <ReactSVG src={informationIcon} />
            <span className="text-xs font-light not-italic leading-[14px] text-txt-inactive">
              Max size of 30 MB, accepted file format: jpg, png or pdf
            </span>
          </div>
        </section>

        <RequiredIndicator />

        <section
          className={cn(
            'sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white pb-6 pt-3',
            mode === 'EDIT' && 'grid-cols-3',
          )}
        >
          <AppButton
            className="grow"
            cosmeticType="skeleton"
            label="Cancel"
            onClick={onClose}
          />
          {mode === 'ADD' && (
            <AppButton
              className="grow"
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateAddNote.isPending}
              label="Add Note"
              type="submit"
            />
          )}
          {mode === 'EDIT' && hasPermission(DELETE_PERMISSION) && (
            <AppButton
              className="grow"
              isLoading={mutateDeleteNote.isPending}
              label="Delete Note"
              onClick={onDeleteNote}
            />
          )}
          {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
            <AppButton
              className="grow"
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditNote.isPending}
              label="Update Note"
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
