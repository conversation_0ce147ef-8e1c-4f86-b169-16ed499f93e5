import numeral from 'numeral'
import { ChangeInfo, type ChangeType } from '@/features/shared/ui'
import { type GetLeadPortfolioStatisticsResponse } from '@/features/shared/services'
import { roundToPrecision } from '@/features/shared/utils'

const currency = 'USD'

interface Customer360BalanceSummaryProps {
  data?: GetLeadPortfolioStatisticsResponse
}

export function Customer360BalanceSummary(
  props: Customer360BalanceSummaryProps,
) {
  const { data } = props
  let changeType = 'default'
  if (Number(data?.change) > 0) {
    changeType = 'incremental'
  } else if (Number(data?.change) < 0) {
    changeType = 'decremental'
  }
  const absChangePercentage = Math.abs(
    roundToPrecision(Number(data?.changePercentage), '00.00'),
  )

  return (
    <section className="space-y-1 rounded-sm bg-bg-default px-3 py-2">
      <div className="text-xs text-txt-label">Total Balance</div>
      <div className="flex justify-between gap-2">
        <div className="flex items-center gap-1">
          <span>{numeral(data?.totalBalance || 0).format('0,0.00')}</span>
          <span className="text-xs font-medium text-txt-inactive">
            {currency}
          </span>
        </div>
        <ChangeInfo
          amount=""
          changeAmount={numeral(data?.change || 0).format('0,0.00')}
          changePercentage={numeral(absChangePercentage).format('0,0.00')}
          changeType={changeType as ChangeType}
          showChange
        />
      </div>
    </section>
  )
}
