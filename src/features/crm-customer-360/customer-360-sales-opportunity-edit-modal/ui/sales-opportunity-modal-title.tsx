import { ReactSVG } from 'react-svg'
import { medalStarIcon } from '@/features/shared/ui/app-icons'

interface SalesOpportunityModalTitleProps {
  name: string
  isVip?: boolean
}
export function SalesOpportunityModalTitle(
  props: SalesOpportunityModalTitleProps,
) {
  const { name = '', isVip = false } = props

  return (
    <div className="flex grow items-center gap-2">
      <span className="text-xs text-txt-paragraph">{name}</span>
      {isVip ? <ReactSVG src={medalStarIcon} /> : null}
    </div>
  )
}
