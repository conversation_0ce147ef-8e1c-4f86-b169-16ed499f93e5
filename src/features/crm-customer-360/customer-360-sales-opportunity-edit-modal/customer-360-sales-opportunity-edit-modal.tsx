import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import {
  AppButton,
  AppInputDatePicker,
  AppInputTimePicker,
  AppModal,
  AppNormalInput,
  AppNormalTextarea,
  ControlledAppSelectInput,
  RequiredIndicator,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import {
  CRMLeadServices,
  type LeadSalesOpportunityActionType,
} from '@/features/shared/services'
import { Customer360SalesOpportunityStatusOptions } from '@/features/shared/mappings'
import { SalesOpportunityModalTitle } from './ui'

const EDIT_PERMISSION = 1600500 // TODO: Update once BE adds permission code

const schema = yup
  .object()
  .shape({
    date: yup.string().required('Date is a required field'),
    details: yup.string(),
    leadId: yup.number(),
    salesId: yup.number(),
    status: yup.string().required('Status is a required field'),
    subject: yup.string().required('Subject is a required field'),
    time: yup.string(),
  })
  .required()

export type SalesOpportunityFormValues = yup.InferType<typeof schema>

interface Customer360SalesOpportunityEditModalProps {
  initialValues: SalesOpportunityFormValues
  leadName: string
  leadIsVip?: boolean
  mode: 'ADD' | 'EDIT'
  onClose: () => void
}
export function Customer360SalesOpportunityEditModal(
  props: Customer360SalesOpportunityEditModalProps,
) {
  const { mode, onClose, initialValues, leadName, leadIsVip } = props
  const { hasPermission } = usePermission()
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const {
    control,
    register,
    reset,
    handleSubmit,
    formState: { isValid, errors },
    setValue,
    watch,
  } = useForm<SalesOpportunityFormValues>({
    defaultValues: {
      ...initialValues,
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })
  const watchedTitle = watch('subject')
  const watchedDate = watch('date')
  const watchedTime = watch('time')

  const mutateAddOpportunity = useMutation({
    mutationFn: CRMLeadServices.addLeadSalesOpportunity,
    onError: () => {
      showToast(
        'Creating Sales Opportunity Failed',
        'Unsuccessful while creating the new sales opportunity. Please try again later.',
        'danger',
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['sales-opportunity-by-lead-id'],
      })
      showToast(
        'Sales Opportunity Created',
        `You have created a new sales opportunity - ${watchedTitle}`,
        'success',
      )
      __onClose()
    },
  })

  const mutateEditOpportunity = useMutation({
    mutationFn: CRMLeadServices.editLeadSalesOpportunity,
    onError: () => {
      showToast(
        'Updating Sales Opportunity Failed',
        `Unsuccessful while updating the sales opportunity - ${watchedTitle}`,
        'danger',
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['sales-opportunity-by-lead-id'],
      })
      showToast(
        'Sales Opportunity Updated',
        `You have updated the sales opportunity - ${watchedTitle}`,
        'success',
      )
      __onClose()
    },
  })

  const __onClose = () => {
    reset({})
    onClose()
  }

  const __onSubmit = (data: SalesOpportunityFormValues) => {
    const dateValue = dayjs(data.date, 'DD-MM-YYYY').format('YYYY-MM-DD')
    const timeValue = data.time
    const dateTime = dayjs(`${dateValue} ${timeValue}`)

    if (mode === 'ADD' && data.leadId) {
      const payload = {
        action: 'ADD' as LeadSalesOpportunityActionType,
        dateTime: dateTime.valueOf(),
        description: data.details,
        leadId: data.leadId,
        salesOpportunityId: data.salesId,
        status: data.status,
        subject: data.subject,
      }
      mutateAddOpportunity.mutateAsync(payload)
    } else if (data.salesId) {
      const payload = {
        action: 'EDIT' as LeadSalesOpportunityActionType,
        dateTime: dateTime.valueOf(),
        description: data.details,
        salesOpportunityId: data.salesId,
        status: data.status,
        subject: data.subject,
      }
      mutateEditOpportunity.mutateAsync(payload)
    }
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleChildren={
        <SalesOpportunityModalTitle isVip={leadIsVip} name={leadName} />
      }
      titleLabel={
        mode === 'ADD'
          ? 'Create New Sales Opportunity'
          : 'Edit Sales Opportunity'
      }
      width={600}
    >
      <form className="flex flex-col gap-3" onSubmit={handleSubmit(__onSubmit)}>
        <AppNormalInput
          label="Subject"
          {...register('subject')}
          error={errors.subject?.message}
          placeholder="Input subject"
          required
        />

        <section className="grid grid-cols-2 gap-2">
          <ControlledAppSelectInput
            control={control}
            label="Status"
            name="status"
            options={Customer360SalesOpportunityStatusOptions}
            placeholder="Select status"
            required
          />
        </section>

        <section className="grid grid-cols-2 gap-2">
          {/* 
            On date selection, set time to 00:00.
            On time selection, set time respectively.
          */}
          <AppInputDatePicker
            allowClear={false}
            hasSuffixIcon={false}
            label="Date"
            onChange={(value?: Dayjs) => {
              setValue(
                'date',
                value?.startOf('date').format('DD-MM-YYYY') ?? '',
                {
                  shouldDirty: true,
                  shouldTouch: true,
                  shouldValidate: true,
                },
              )
            }}
            required
            value={watchedDate ? dayjs(watchedDate, 'DD-MM-YYYY') : undefined}
          />
          <AppInputTimePicker
            allowClear={false}
            label="Time"
            labelDescription="(24 Hours)"
            onChange={(value?: Dayjs) => {
              setValue('time', value?.format('HH:mm') ?? '')
            }}
            value={watchedTime ? dayjs(watchedTime, 'HH:mm') : undefined}
          />
        </section>

        <AppNormalTextarea
          label="Details"
          wrapperClassName="-mt-0.5"
          {...register('details')}
          error={errors.details?.message}
          placeholder="Input Details"
        />

        <RequiredIndicator />

        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white pb-6 pt-3">
          <AppButton cosmeticType="skeleton" label="Cancel" onClick={onClose} />
          {mode === 'ADD' && (
            <AppButton
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateAddOpportunity.isPending}
              label="Create"
              type="submit"
            />
          )}
          {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
            <AppButton
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditOpportunity.isPending}
              label="Save Changes"
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
