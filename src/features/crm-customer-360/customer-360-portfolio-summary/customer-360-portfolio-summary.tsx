import numeral from 'numeral'
import {
  AppInfoCardVariant9,
  AppInfoContainerCollapse,
} from '@/features/shared/ui'
import { type GetLeadPortfolioStatisticsResponse } from '@/features/shared/services'

interface Customer360PortfolioSummaryProps {
  data?: GetLeadPortfolioStatisticsResponse
}

export function Customer360PortfolioSummary(
  props: Customer360PortfolioSummaryProps,
) {
  const { data } = props

  return (
    <AppInfoContainerCollapse
      headerChildren={
        <h4 className="inline-block text-xs font-medium">Portfolio Summary</h4>
      }
    >
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Cash Available',
            value: (
              <div>
                {numeral(data?.cashAvailable || 0).format('0,0.00')}
                <span className="ml-1 text-[10px] font-medium text-txt-inactive">
                  {data?.currency}
                </span>
              </div>
            ),
          },
          {
            label: 'Total Portfolio AUM',
            value: (
              <div>
                {numeral(data?.assetValue || 0).format('0,0.00')}
                <span className="ml-1 text-[10px] font-medium text-txt-inactive">
                  {data?.currency}
                </span>
              </div>
            ),
          },
          {
            label: 'Annualized Return',
            value: numeral(data?.annualizedReturn || 0).format('0,0.00%'),
          },
          {
            label: 'Annualized Volatility',
            value: numeral(data?.annualizedVolatility || 0).format('0,0.00%'),
          },
          {
            label: 'MTD return',
            value: numeral(data?.mtdReturn ? Number(data.mtdReturn) : 0).format(
              '0,0.00%',
            ),
          },
          {
            label: 'YTD return',
            value: numeral(data?.ytdReturn ? Number(data.ytdReturn) : 0).format(
              '0,0.00%',
            ),
          },
          {
            label: 'ITD Return',
            value: numeral(data?.itdReturn ? Number(data.itdReturn) : 0).format(
              '0,0.00%',
            ),
          },
          {
            label: 'Beta',
            value: numeral(data?.beta || 0).format('0,0.00'),
          },
          {
            label: 'Sharpe',
            value: numeral(data?.sharpe || 0).format('0,0.00'),
          },
        ]}
      />
    </AppInfoContainerCollapse>
  )
}
