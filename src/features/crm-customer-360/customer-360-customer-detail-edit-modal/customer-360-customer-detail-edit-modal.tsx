import { useFieldArray, useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { useEffect, type ChangeEvent } from 'react'
import {
  AppButton,
  AppInfoCardVariant9,
  AppInputDatePicker,
  AppModal,
  AppNormalInput,
  AppToggleInput,
  ControlledAppChoiceInput,
  ControlledAppSelectInput,
  FilterTag,
  RequiredIndicator,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import {
  LEAD_PRIORITY_OPTIONS,
  LEAD_STATUS_OPTIONS,
  LEAD_TYPE_OPTIONS,
  LeadTypes,
} from '@/features/shared/mappings'
import {
  CommonServices,
  CRMLeadServices,
  type LeadDetailActionType,
  type GetEnumerationResponse,
  type SearchTagListResponse,
  type LeadPriority,
  type LeadType,
  type LeadStatus,
} from '@/features/shared/services'
import {
  ControlledCustomerSearchInput,
  type CustomerSearchInputOption,
  TagSearchInput,
} from '@/features/shared/widgets'

const EDIT_PERMISSION = 1600500

const getOptions = (
  code: string,
  enumeration?: GetEnumerationResponse,
): { label: string; value: string }[] | undefined => {
  return enumeration?.list
    .find((x) => x.code === code)
    ?.list.map((x) => ({
      label: x.desc,
      value: x.value,
    }))
}

const schema = yup
  .object()
  .shape({
    address: yup.string(),
    birthday: yup.number(),
    countryRegion: yup.string(),
    customerId: yup.number().when('type', ([value]) => {
      return value === LeadTypes.CUSTOMER.key
        ? yup
            .number()
            .min(1, 'Customer Id is required')
            .required('Customer Id is required')
        : yup.number()
    }),
    emailAddress: yup
      .string()
      .email()
      .required('Email Address is a required field'),
    isVip: yup.boolean().required(),
    leadId: yup.number().required(),
    name: yup.string().required('Name is a required field'),
    phoneNumber: yup.string(),
    priority: yup.string().required('Priority is a required field'),
    status: yup.string().required('Lead Status is a required field'),
    tags: yup
      .array()
      .of(yup.object().shape({ label: yup.string().required() })),
    type: yup.string().required('Lead Type is a required field'),
  })
  .required()

export type FormValues = yup.InferType<typeof schema>

interface Customer360CustomerDetailEditModalProps {
  initialValues: FormValues
  onClose: () => void
}
export function Customer360CustomerDetailEditModal(
  props: Customer360CustomerDetailEditModalProps,
) {
  const { initialValues, onClose } = props
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const { hasPermission } = usePermission()

  const {
    formState: { isValid, errors },
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      ...initialValues,
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })
  const { append, remove } = useFieldArray({
    control,
    name: 'tags',
  })
  const watchedBirthday = watch('birthday')
  const watchedName = watch('name')
  const watchedLeadId = watch('leadId')
  const watchedIsVIP = watch('isVip')
  const watchedTags = watch('tags')
  const watchedType = watch('type')

  const { data: enumeration } = useQuery({
    queryFn: () =>
      CommonServices.getEnumeration({
        codeList: ['REGION'],
      }),
    queryKey: ['region', 'bank-account-type', 'currency'],
  })
  const countryListOptions = getOptions('REGION', enumeration)

  const mutateEditLeadDetail = useMutation({
    mutationFn: CRMLeadServices.editLeadDetail,
    onError: (error) => {
      showToast('Lead Details Update Unsuccessful', error.message, 'danger')
    },
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['lead-detail-by-id'],
      })
      showToast(
        'Lead Details Updated Successfully',
        `You have updated a lead - ${watchedName}`,
        'success',
      )
    },
  })

  const __onClose = () => {
    reset({})
    onClose()
  }

  const __onSubmit = handleSubmit((data: FormValues) => {
    if (isValid) {
      const payload = {
        action: 'EDIT' as LeadDetailActionType,
        address: data.address ?? '',
        birthday: data.birthday
          ? dayjs(data.birthday).format('DD-MM-YYYY')
          : undefined,
        countryRegion: data.countryRegion,
        customerId: data.type === 'CUSTOMER' ? data.customerId : undefined,
        emailAddress: data.emailAddress,
        isVip: data.isVip ? 'TRUE' : 'FALSE',
        leadId: data.leadId,
        name: data.name,
        phoneNumber: data.phoneNumber,
        priority: data.priority as LeadPriority,
        status: data.status as LeadStatus,
        tags: data.tags?.map((tag) => tag.label) ?? [],
        type: data.type as LeadType,
      }
      mutateEditLeadDetail.mutateAsync(payload)
    }
  })

  const onRemoveTag = (index: number) => {
    remove(index)
  }
  const onSelectTag = (tag: SearchTagListResponse) => {
    if (watchedTags?.findIndex((c) => c.label === tag.tag) === -1) {
      append({ label: tag.tag })
    }
  }
  const onSelectCustomer = (customer: CustomerSearchInputOption) => {
    setValue('customerId', customer.customerId, {
      shouldValidate: true,
    })
  }

  useEffect(() => {
    setValue('customerId', undefined, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    })
  }, [setValue, watchedType])

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleChildren={
        <div className="flex grow items-center justify-end gap-1">
          <span className="text-xs font-light">VIP</span>
          <AppToggleInput
            checked={watchedIsVIP}
            disabled={!hasPermission(EDIT_PERMISSION)}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              e.stopPropagation()
              setValue('isVip', e.target.checked)
            }}
          />
        </div>
      }
      titleDescription={watchedName}
      titleLabel="View/Edit Lead"
      width={600}
    >
      <form className="flex flex-col gap-3" onSubmit={__onSubmit}>
        <AppInfoCardVariant9
          containerClassName="mb-1"
          contents={[{ label: 'Lead ID', value: watchedLeadId }]}
        />
        <div className="grid grid-cols-2 gap-2">
          <AppNormalInput
            label="Name"
            {...register('name')}
            error={errors.name?.message}
            placeholder="Input Name"
            required
          />
          {/* NOTE: Empty tag to take up space */}
          <div />
          <AppNormalInput
            label="Email Address"
            {...register('emailAddress')}
            error={errors.emailAddress?.message}
            placeholder="Input Email Address"
            required
          />

          <AppNormalInput
            label="Phone Number"
            {...register('phoneNumber')}
            error={errors.phoneNumber?.message}
            placeholder="Input Phone Number"
          />

          <AppInputDatePicker
            label="Birthday"
            onChange={(value?: Dayjs) => {
              setValue('birthday', value?.valueOf() ?? 0, {
                shouldDirty: true,
                shouldTouch: true,
              })
            }}
            value={watchedBirthday ? dayjs(watchedBirthday) : undefined}
          />

          <ControlledAppSelectInput
            control={control}
            label="Country/Region"
            name="countryRegion"
            optionFilterProp="label"
            options={countryListOptions}
            placeholder="Select country/region"
            showSearch
          />
        </div>

        <AppNormalInput
          label="Address"
          {...register('address')}
          error={errors.address?.message}
          placeholder="Input Address"
        />

        <div className="grid grid-cols-2 gap-2">
          <ControlledAppChoiceInput
            cols={3}
            control={control}
            label="Priority"
            name="priority"
            options={LEAD_PRIORITY_OPTIONS.map((item) => ({
              label: item.label,
              value: item.value,
              valueStyle: {
                color: item.labelColor,
              },
            }))}
            required
          />
          <ControlledAppChoiceInput
            cols={2}
            control={control}
            label="Status"
            name="status"
            options={LEAD_STATUS_OPTIONS.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-2">
          <ControlledAppChoiceInput
            cols={2}
            control={control}
            label="Type"
            name="type"
            options={LEAD_TYPE_OPTIONS.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
            required
          />
          {watchedType === 'CUSTOMER' && (
            <ControlledCustomerSearchInput
              control={control}
              label="Customer ID"
              name="customerId"
              onSelect={onSelectCustomer}
            />
          )}
        </div>

        <TagSearchInput label="Tags" onSelect={onSelectTag} />
        <div className="flex flex-wrap items-center gap-1.5">
          {watchedTags?.map((tag, index) => (
            <FilterTag
              key={index}
              onClick={() => {
                onRemoveTag(index)
              }}
              title={tag.label}
            />
          ))}
        </div>

        <RequiredIndicator />

        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white py-6">
          <AppButton
            className="grow"
            cosmeticType="skeleton"
            label="Cancel"
            onClick={onClose}
          />

          {hasPermission(EDIT_PERMISSION) && (
            <AppButton
              className="grow"
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditLeadDetail.isPending}
              label={mutateEditLeadDetail.isPending ? 'Saving' : 'Save Changes'}
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
