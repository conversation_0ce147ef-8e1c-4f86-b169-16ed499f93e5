import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  CRMMessageCenterServices,
  MESSAGE_CENTER_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetMessageListResponse,
  type MessageStatus,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
  eyeIcon,
  sendIcon,
} from '@/features/shared/ui/app-icons'
import { MessageStatusMapping as MessageStatusMapping } from '@/features/shared/mappings'

const VIEW_MESSAGE_PERMISSION = 2200200
const DELETE_MESSAGE_PERMISSION = 2200300
const PUBLISH_MESSAGE_PERMISSION = 2200400

interface MessageCenterListTableProps {
  onRowClick: (
    record: GetMessageListResponse,
    action: 'VIEW' | 'EDIT' | 'DELETE' | 'PUBLISH',
  ) => void
}

export function MessageCenterListTable(props: MessageCenterListTableProps) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()
  const [searchParams] = useSearchParams()

  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)
  const statusQuery = searchParams.get(SEARCH_PARAMS.status)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      CRMMessageCenterServices.getMessageList({
        startTime: createFromTimeQuery
          ? dayjs(Number(createFromTimeQuery)).valueOf()
          : null,
        endTime: createToTimeQuery
          ? dayjs(Number(createToTimeQuery)).valueOf()
          : null,
        searchValue: keywordQuery || '',
        pageNo: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        searchType: keywordQuery ? searchTypeQuery || 'TITLE' : '',
        sortBy: sortByQuery || 'created_at',
        sortType: sortTypeQuery || 'desc',
        status: statusQuery ? (Number(statusQuery) as MessageStatus) : undefined,
      }),
    queryKey: [
      'message-center-list',
      keywordQuery,
      searchTypeQuery,
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
      createFromTimeQuery,
      createToTimeQuery,
      statusQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const columns: TableProps<GetMessageListResponse>['columns'] = [
    {
      dataIndex: 'title',
      key: 'TITLE',
      sortOrder:
        sortByQuery === 'TITLE'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: false,
      title: 'Message Title',
      width: 200,
    },
    {
      dataIndex: 'content',
      key: 'contentSummary',
      render: (contentSummary: string) => (
        <span className="text-gray-600">
          {contentSummary || '-'}
        </span>
      ),
      title: 'Content Summary',
      width: 300,
    },
    {
      dataIndex: 'status',
      key: 'STATUS',
      render: (status: MessageStatus) => (
        <span
          className="font-light"
          style={{
            color: MessageStatusMapping[status]?.color ?? '#000',
          }}
        >
          {MessageStatusMapping[status]?.label ?? '-'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'STATUS'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Status',
      width: 120,
    },
    {
      dataIndex: 'created',
      key: 'created_at',
      render: (createTime: number) => (
        <span>
          {createTime ? dayjs(createTime * 1000).format('YYYY-MM-DD HH:mm:ss') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'created_at'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Created Date',
      width: 150,
    },
    {
      dataIndex: 'id',
      key: 'id',
      render: (id: number, record) => (
        <Popover
          arrow={false}
          content={
            <div className="flex flex-col">
              {hasPermission(VIEW_MESSAGE_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="View Message"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'VIEW')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={eyeIcon}
                  type="button"
                />
              )}

              {hasPermission(VIEW_MESSAGE_PERMISSION) && record.status === 0 && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Edit Message"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'EDIT')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={editIcon}
                  type="button"
                />
              )}

              {hasPermission(PUBLISH_MESSAGE_PERMISSION) && (record.status == 0 || record.status == 1 || record.status == 4 || record.status == 5) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Publish Message"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'PUBLISH')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={sendIcon}
                  type="button"
                />
              )}

              {hasPermission(DELETE_MESSAGE_PERMISSION) && record.status === 0 && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Delete Message"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'DELETE')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={trashIcon}
                  type="button"
                />
              )}
            </div>
          }
          onOpenChange={(newOpen) => {
            handleOpenChange(newOpen, id)
          }}
          open={selectedPopover === id}
          placement="bottomRight"
          trigger="click"
        >
          <AppButton
            cosmeticType="transparent"
            onClick={(e) => {
              e.stopPropagation()
            }}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={dotsThreeVerticalIcon}
              />
            }
            type="button"
          />
        </Popover>
      ),
      title: ' ',
      width: 40,
    },
  ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="id"
      scroll={{ x: 1000 }}
      total={data?.total}
    />
  )
}