import { useRecoilState } from 'recoil'
import { AppButton } from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { messageCenterDetailState } from '@/features/shared/states'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'

const CREATE_MESSAGE_PERMISSION = 2200100

export function MessageCenterContentHeader() {
  const { hasPermission } = usePermission()
  const [, setMessageCenterDetailState] = useRecoilState(messageCenterDetailState)

  const openMessageModal = () => {
    setMessageCenterDetailState((prev) => ({
      ...prev,
      mode: 'ADD',
      selectedMessage: undefined,
    }))
  }

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Message Center</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage and send messages to customers
        </p>
      </div>
      {hasPermission(CREATE_MESSAGE_PERMISSION) && (
        <AppButton
            className="mr-2"
            label="Add"
            onClick={openMessageModal}
            prefixIcon={plusGreenIcon}
          />
      )}
    </div>
  )
}