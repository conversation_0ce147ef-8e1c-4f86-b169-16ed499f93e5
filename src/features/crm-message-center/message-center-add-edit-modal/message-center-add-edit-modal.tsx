import { useEffect, useState, useMemo } from 'react'
import { useRecoilState } from 'recoil'
import { useForm, useFieldArray } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { messageCenterDetailState, type MessageCenterDetailState } from '@/features/shared/states'
import {
    AppButton,
    AppModal,
    AppNormalInput,
    AppNormalTextarea,
    AppSelect,
    ControlledAppSelectInput,
    RequiredIndicator,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import { CHANNEL_TYPE_OPTIONS } from '@/features/shared/mappings'
import {
    CRMMessageCenterServices,
    type PublishChannel,
    ChannelType,
} from '@/features/shared/services'
import { trashIcon } from '@/features/shared/ui/app-icons'
import { ReactSVG } from 'react-svg'


const ADD_PERMISSION = 1800100
const EDIT_PERMISSION = 1800300

interface FormValues {
    title: string
    content: string
    publishChannels: PublishChannel[]
}

const schema: yup.ObjectSchema<FormValues> = yup
    .object()
    .shape({
        title: yup.string().required('Message title is required').max(50, 'Message title must be less than 50 characters'),
        content: yup.string().required('Message content is required').max(500, 'Message content must be less than 500 characters'),
        publishChannels: yup.array().of(
            yup.object().shape({
                channelType: yup.mixed<ChannelType>().oneOf([0 as ChannelType]).required('Channel type is required'),
                target: yup.string().required('Audience is required'),
                userIds: yup.string().required('Audience is required'),
            })
        ).min(1, 'At least one publish channel is required').required(),
    })
    .required()

export function MessageCenterAddEditModal() {
    const queryClient = useQueryClient()
    const { showToast } = useToast()
    const { hasPermission } = usePermission()
    const [{ mode, selectedMessage }, setMessageCenterDetailState] =
        useRecoilState<MessageCenterDetailState>(messageCenterDetailState)

    const { data: messageData } = useQuery({
        enabled: Boolean(selectedMessage?.id && mode === 'EDIT'),
        queryFn: () =>
            CRMMessageCenterServices.getMessageDetail({
                id: Number(selectedMessage?.id),
            }),
        queryKey: ['message-detail-by-id', selectedMessage?.id],
    })

    const {
        formState: { isValid, errors },
        register,
        control,
        handleSubmit,
        reset,
        watch,
        setValue, // 添加 setValue
    } = useForm<FormValues>({
        defaultValues: {
            title: '',
            content: '',
            publishChannels: [{
                channelType: 0 as ChannelType,
                target: '*',
                userIds: '*', // 添加默认值
            }],
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
        resolver: yupResolver(schema),
    })

    const { fields, remove } = useFieldArray({
        control,
        name: 'publishChannels',
    })

    const watchedTitle = watch('title')

    useEffect(() => {
        if (messageData) {
            reset({
                title: messageData.title,
                content: messageData.content,
                publishChannels: messageData.publishChannels?.length > 0
                    ? messageData.publishChannels
                    : [{ channelType: 0 as ChannelType, target: '' }],
            })
            
            // 处理编辑模式下的 userIds 数据
            if (messageData.publishChannels && messageData.publishChannels.length > 0) {
                const firstChannel = messageData.publishChannels[0]
                if (firstChannel && firstChannel.userIds && firstChannel.userIds !== '*') {
                    // 如果 userIds 不是 "*"，按逗号分隔并设置到多选框
                    const userIdArray = firstChannel.userIds.split(',').filter(id => id.trim() !== '')
                    setSelectedCustomerIds(userIdArray)
                } else {
                    // 如果是 "*" 或为空，设置为 "*"
                    setSelectedCustomerIds(['*'])
                }
            }
        }
    }, [messageData, reset])

    // 获取客户列表
    const { data: customerListData } = useQuery({
        queryFn: () => CRMMessageCenterServices.getCustomerList(),
        queryKey: ['customer-list-for-message'],
    })

    // 处理客户选项数据
    const customerOptions = useMemo(() => {
        if (!customerListData?.customerList) return [{
            label: 'all',
            value: '*',
            key: '*',
            customerData: null
        }]
        
        const defaultOption = {
            label: 'all',
            value: '*',
            key: '*',
            customerData: null
        }
        
        const customerList = customerListData.customerList.map(customer => ({
            label: `${customer.code}-${customer.name}`,
            value: customer.id.toString(),
            key: customer.id.toString(),
            customerData: customer
        }))
        
        return [defaultOption, ...customerList]
    }, [customerListData])

    // 存储选中的客户ID列表
    const [selectedCustomerIds, setSelectedCustomerIds] = useState<string[]>([])

    // 根据选中的ID获取对应的rid列表
    const getSelectedRids = (selectedIds: string[]) => {
        if (!customerListData?.customerList) return ''
        const rids: string[] = []
        selectedIds.forEach(id => {
            const customer = customerListData.customerList.find(c => c.id.toString() === id)
            if (customer && customer.rid.length > 0) {
                rids.push(...customer.rid)
            }
        })
        return rids.join(',')
    }

    const mutateCreateMessage = useMutation({
        mutationFn: CRMMessageCenterServices.createMessage,
        onError: (error) => {
            showToast('Message Creation Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Message Created Successfully', 'success')
        },
    })

    const mutateUpdateMessage = useMutation({
        mutationFn: CRMMessageCenterServices.updateMessage,
        onError: (error) => {
            showToast('Message Update Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Message Updated Successfully', 'success')
        },
    })

    const mutateCreateAndPublishMessage = useMutation({
        mutationFn: CRMMessageCenterServices.createAndPublishMessage,
        onError: (error) => {
            showToast('Message Creation and Publish Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Message Created and Publish Successfully', 'success')
        },
    })

    const mutateUpdateAndPublishMessage = useMutation({
        mutationFn: CRMMessageCenterServices.updateAndPublishMessage,
        onError: (error) => {
            showToast('Message Update and Publish Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Message Updated and Publish Successfully', 'success')
        },
    })

    const __onClose = () => {
        reset({})
        setMessageCenterDetailState((prev) => ({
            ...prev,
            mode: undefined,
            selectedMessage: undefined,
        }))
    }

    const __onSave = handleSubmit((data: FormValues) => {
        if (isValid && data.publishChannels && data.publishChannels.length > 0) {
            const payload = {
                title: data.title,
                content: data.content,
                publishChannels: data.publishChannels,
                action: 'SAVE' as const,
            }

            if (mode === 'ADD') {
                mutateCreateMessage.mutateAsync(payload)
            } else if (mode === 'EDIT' && selectedMessage?.id) {
                mutateUpdateMessage.mutateAsync({
                    id: selectedMessage.id,
                    title: payload.title,
                    content: payload.content,
                    publishChannels: payload.publishChannels,
                    action: payload.action,
                })
            }
        }
    })

    const __onSaveAndPublish = handleSubmit((data: FormValues) => {
        if (isValid && data.publishChannels && data.publishChannels.length > 0) {
            const payload = {
                title: data.title,
                content: data.content,
                publishChannels: data.publishChannels,
                action: 'PUBLISH' as const,
            }

            if (mode === 'ADD') {
                mutateCreateAndPublishMessage.mutateAsync(payload)
            } else if (mode === 'EDIT' && selectedMessage?.id) {
                mutateUpdateAndPublishMessage.mutateAsync({
                    id: selectedMessage.id,
                    title: payload.title,
                    content: payload.content,
                    publishChannels: payload.publishChannels,
                    action: payload.action,
                })
            }
        }
    })

    return (
        <AppModal
            childrenWrapperClassName="pb-0" // required for sticky footer
            onCancel={__onClose}
            open
            // titleDescription={watchedTitle}
            titleLabel={mode === 'ADD' ? 'Create Message' : 'Edit Message'}
            width={600}
        >
            <form className="flex flex-col gap-3" onSubmit={__onSave}>
                {/* <AppInfoCardVariant9
                    containerClassName="mb-1"
                    contents={[{ label: 'Message ID', value: selectedMessage?.id }]}
                /> */}

                <AppNormalInput
                    label="Message Title"
                    {...register('title')}
                    error={errors.title?.message}
                    placeholder="Enter message title"
                    required
                />

                <AppNormalTextarea
                    label="Message Content"
                    {...register('content')}
                    error={errors.content?.message}
                    placeholder="Enter message content"
                    required
                    rows={6}
                />

                <div>
                    <div className="flex items-center justify-between mb-4">
                        <label className="block text-sm font-medium text-gray-700">
                            Publish Channels <span className="text-red-500">*</span>
                        </label>
                        {/* <AppButton
                            cosmeticType="tertiary"
                            label="Add Channel"
                            // onClick={() => append({ channelType: 0 as PublishChannel, recipients: '' })}
                            onClick={() => console.log('add channel')}
                            prefixIcon={plusIcon}
                            size="s"
                            type="button"
                        /> */}
                    </div>

                    <div className="space-y-3">
                        {fields.map((field, index) => (
                            <div key={field.id} className="border p-4 rounded-md relative">
                                <ControlledAppSelectInput
                                    control={control}
                                    name={`publishChannels.${index}.channelType`}
                                    label="Channel Type"
                                    options={CHANNEL_TYPE_OPTIONS}
                                    defaultValue={0 as ChannelType}
                                    required
                                />
                                <div className="relative">
                                    <AppSelect
                                        mode="multiple"
                                        placeholder="Select customers"
                                        options={customerOptions}
                                        value={selectedCustomerIds}
                                        onChange={(value) => {
                                            const selectedIds = value as string[]
                                            let finalSelectedIds: string[] = []
                                            
                                            // 获取新增的选项（当前选择与之前选择的差集）
                                            const newlySelected = selectedIds.filter(id => !selectedCustomerIds.includes(id))
                                            const newlyDeselected = selectedCustomerIds.filter(id => !selectedIds.includes(id))
                                            
                                            if (newlySelected.length > 0) {
                                                // 有新选择的项目
                                                const lastSelected = newlySelected[newlySelected.length - 1]
                                                
                                                if (lastSelected === '*') {
                                                    // 如果新选择的是 "*"，只保留 "*"
                                                    finalSelectedIds = ['*']
                                                } else {
                                                    // 如果新选择的是其他选项，移除 "*" 并添加新选项
                                                    finalSelectedIds = selectedIds.filter(id => id !== '*')
                                                }
                                            } else if (newlyDeselected.length > 0) {
                                                // 只是取消选择，保持当前选择
                                                finalSelectedIds = selectedIds
                                            } else {
                                                // 其他情况，保持当前选择
                                                finalSelectedIds = selectedIds
                                            }
                                            
                                            setSelectedCustomerIds(finalSelectedIds)
                                            
                                            // 同步更新表单字段
                                            if (finalSelectedIds.includes('*')) {
                                                // 如果选择了 "*"，设置为 "*"
                                                setValue(`publishChannels.${index}.target`, '*')
                                                setValue(`publishChannels.${index}.userIds`, '*')
                                            } else {
                                                // 否则，根据选中的ID获取对应的rid列表
                                                const rids = getSelectedRids(finalSelectedIds)
                                                setValue(`publishChannels.${index}.target`, rids)
                                                setValue(`publishChannels.${index}.userIds`, finalSelectedIds.join(','))
                                            }
                                        }}
                                        className="w-full customer-select-vertical-center cursor-pointer"
                                        style={{ 
                                            height: '40px', 
                                            minHeight: '40px',
                                            cursor: 'pointer'
                                        }}
                                        label="Customer"
                                        maxTagCount="responsive"
                                        showSearch  // 已启用搜索功能
                                        aria-required={true}
                                        filterOption={(input, option) =>  // 已配置搜索过滤逻辑
                                            String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                        }
                                    />
                                </div>
                                {index > 0 && (
                                    <button
                                        type="button"
                                        onClick={() => remove(index)}
                                        className="absolute top-4 right-4 p-1 text-gray-400 hover:text-red-500 transition-colors"
                                        title="删除此频道"
                                    >
                                        <ReactSVG
                                            beforeInjection={(svg) => {
                                                svg.classList.add('w-4', 'h-4', 'fill-current')
                                            }}
                                            src={trashIcon}
                                        />
                                    </button>
                                )}
                            </div>
                        ))}
                    </div>

                    {errors.publishChannels && (
                        <p className="text-red-500 text-sm mt-1">
                            {errors.publishChannels.message}
                        </p>
                    )}
                </div>

                <RequiredIndicator />

                <section className="sticky bottom-0 left-0 right-0 grid grid-cols-3 gap-2 bg-white py-6">
                    <AppButton
                        cosmeticType="skeleton"
                        label="Cancel"
                        onClick={__onClose}
                    />

                    {mode === 'ADD' && hasPermission(ADD_PERMISSION) && (
                        <>
                            <AppButton
                                cosmeticType="tertiary"
                                disabled={!isValid}
                                isLoading={mutateCreateMessage.isPending}
                                label="Save Draft"
                                onClick={__onSave}
                                type="button"
                            />
                            <AppButton
                                cosmeticType="secondary"
                                disabled={!isValid}
                                isLoading={mutateCreateMessage.isPending}
                                label="Save & Publish"
                                onClick={__onSaveAndPublish}
                                type="button"
                            />
                        </>
                    )}

                    {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
                        <>
                            <AppButton
                                cosmeticType="tertiary"
                                disabled={!isValid}
                                isLoading={mutateUpdateMessage.isPending}
                                label="Save Draft"
                                onClick={__onSave}
                                type="button"
                            />
                            <AppButton
                                cosmeticType="secondary"
                                disabled={!isValid}
                                isLoading={mutateUpdateMessage.isPending}
                                label="Save & Publish"
                                onClick={__onSaveAndPublish}
                                type="button"
                            />
                        </>
                    )}
                </section>
            </form>
        </AppModal>
    )
}