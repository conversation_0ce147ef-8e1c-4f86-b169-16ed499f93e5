import dayjs from 'dayjs'
import { useRecoilState } from 'recoil'
import { useQuery } from '@tanstack/react-query'
import { messageCenterDetailState } from '@/features/shared/states'
import { AppButton, AppModal, AppNormalInput, AppNormalTextarea } from '@/features/shared/ui'
import { CRMMessageCenterServices } from '@/features/shared/services'
import { CHANNEL_TYPE_OPTIONS } from '@/features/shared/mappings'

export function MessageCenterViewModal() {
  const [{ selectedMessage }, setMessageCenterDetailState] = useRecoilState(
    messageCenterDetailState,
  )

  const { data: messageData } = useQuery({
    enabled: Boolean(selectedMessage?.id),
    queryFn: () =>
      CRMMessageCenterServices.getMessageDetail({
        id: Number(selectedMessage?.id),
      }),
    queryKey: ['message-detail-by-id', selectedMessage?.id],
  })

  const __onClose = () => {
    setMessageCenterDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedMessage: undefined,
    }))
  }

  const getChannelTypeLabel = (channelType: number | undefined) => {
    if (channelType === undefined) return '-'
    const option = CHANNEL_TYPE_OPTIONS.find(opt => opt.value === channelType)
    return option?.label || '-'
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0"
      onCancel={__onClose}
      open
      // titleDescription={messageData?.title}
      titleLabel="Message Details"
      width={600}
    >
      <div className="flex flex-col gap-3">
        <AppNormalInput
          label="Message Title"
          value={messageData?.title || ''}
          placeholder="-"
          readOnly
          disabled
        />

        <AppNormalTextarea
          label="Message Content"
          value={messageData?.content || ''}
          placeholder="-"
          readOnly
          disabled
          rows={6}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Publish Channels
          </label>
          
          <div className="space-y-3">
            {messageData?.publishChannels && messageData.publishChannels.length > 0 ? (
              messageData.publishChannels.map((channel, index) => (
                <div key={index} className="border p-4 rounded-md bg-gray-50">
                  <AppNormalInput
                    label="Channel Type"
                    value={getChannelTypeLabel(channel.channelType)}
                    placeholder="-"
                    readOnly
                    disabled
                  />
                  <AppNormalInput
                    label="Audience"
                    value={channel.target || ''}
                    placeholder="-"
                    readOnly
                    disabled
                  />
                </div>
              ))
            ) : (
              <div className="border p-4 rounded-md bg-gray-50">
                <p className="text-gray-500 text-sm">No publish channels</p>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <AppNormalInput
            label="Created Time"
            value={messageData?.created
              ? dayjs(messageData.created * 1000).format('YYYY-MM-DD HH:mm:ss')
              : '-'
            }
            readOnly
            disabled
          />
          <AppNormalInput
            label="Published Time"
            value={messageData?.published
              ? dayjs(messageData.published * 1000).format('YYYY-MM-DD HH:mm:ss')
              : '-'
            }
            readOnly
            disabled
          />
        </div>

        <section className="sticky bottom-0 left-0 right-0 flex justify-end bg-white py-6">
          <AppButton
            cosmeticType="primary"
            label="Close"
            onClick={__onClose}
            type="button"
          />
        </section>
      </div>
    </AppModal>
  )
}