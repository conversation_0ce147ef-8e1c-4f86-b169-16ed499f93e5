import { useRecoilState } from 'recoil'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { messageCenterDetailState } from '@/features/shared/states'
import { AppButton, AppModal } from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import { CRMMessageCenterServices } from '@/features/shared/services'

export function MessageCenterPublishModal() {
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const [{ selectedMessage }, setMessageCenterDetailState] = useRecoilState(
    messageCenterDetailState,
  )

  const mutatePublishMessage = useMutation({
    mutationFn: CRMMessageCenterServices.publishMessage,
    onError: (error) => {
      showToast('Message Published Failed', error.message, 'danger')
    },
    onSuccess: () => {
      __onClose()
      queryClient.invalidateQueries({
        queryKey: ['message-center-list'],
      })
      showToast('Message Published Successfully', 'success')
    },
  })

  const __onClose = () => {
    setMessageCenterDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedMessage: undefined,
    }))
  }

  const __onConfirm = () => {
    if (selectedMessage?.id) {
      mutatePublishMessage.mutateAsync({
        messageId: selectedMessage.id,
        action: 'PUBLISH',
      })
    }
  }

  return (
    <AppModal
      onCancel={__onClose}
      open
      titleLabel="Delete Message"
      width={500}
    >
      <div className="flex flex-col gap-4">
        <p className="text-gray-700">
          Are you sure you want to publish the message "{selectedMessage?.title}"?
          This action cannot be undone.
        </p>

        <div className="flex justify-end gap-2">
          <AppButton
            cosmeticType="secondary"
            label="Cancel"
            onClick={__onClose}
            type="button"
          />
          <AppButton
            label="Publish"
            onClick={__onConfirm}
            type="button"
          />
        </div>
      </div>
    </AppModal>
  )
}