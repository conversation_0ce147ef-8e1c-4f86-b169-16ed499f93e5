import Select, { type BaseOptionType } from 'antd/es/select'
import { useSearchParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { useState, useEffect } from 'react'
import { useDebounceValue } from 'usehooks-ts'
import { AppInputBeta } from '@/features/shared/ui/app-input-beta'
import { MESSAGE_CENTER_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import {
    arrowClockwiseIcon,
    caretDownIcon,
    magnifyingGlassIcon,
    slidersHorizontalIcon,
} from '@/features/shared/ui/app-icons'
import {
    AppButton,
    AppInputDateRangePicker,
} from '@/features/shared/ui'
import { NoUndefinedRangeValueType } from 'rc-picker/lib/PickerInput/RangePicker'
import dayjs, { Dayjs } from 'dayjs'

interface MessageCenterRefineryAndSearchProps {
    onRefineListClick: () => void
}

export function MessageCenterRefineryAndSearch(
    props: MessageCenterRefineryAndSearchProps,
) {

    const FILTER_OPTIONS: BaseOptionType[] = [
        {
            label: 'Title',
            value: 'Title',
        },
    ]

    interface ResetRefineryButtonProps {
        resetKeyword: () => void
    }
    function ResetRefineryButton({ resetKeyword }: ResetRefineryButtonProps) {
        const [, setSearchParams] = useSearchParams()

        const resetRefinery = () => {
            setSearchParams(() => {
                return {}
            })
            resetKeyword()
        }

        return (
            <AppButton
                label="Reset"
                onClick={resetRefinery}
                prefixIcon={
                    <ReactSVG
                        beforeInjection={(svg) => {
                            svg.classList.add('fill-txt-title')
                        }}
                        src={arrowClockwiseIcon}
                    />
                }
            />
        )
    }
    const resetKeyword = () => {
        setKeyword('')
    }

    const { onRefineListClick } = props
    const [searchParams, setSearchParams] = useSearchParams()
    const keywordQuery = searchParams.get('keyword')
    const [keyword, setKeyword] = useState(keywordQuery || '')
    const [debouncedKeyword] = useDebounceValue(
        keyword || '',
        300,
    )
    const searchTypeQuery = searchParams.get('searchType')
    const [searchType, setSearchType] = useState(searchTypeQuery)
    const adjustedSearchParamSize = searchParams.get('searchType')
        ? searchParams.size - 1
        : searchParams.size
    const createFromTimeQuery = searchParams.get("createFromTime")
    const createToTimeQuery = searchParams.get('createToTime')

    const onRefineStartDate = (
        dates: NoUndefinedRangeValueType<Dayjs> | null,
    ) => {
        if (dates) {
            const [startFrom, startTo] = dates
            setSearchParams((searchParams) => {
                updateParam(
                    SEARCH_PARAMS.createFromTime,
                    String(startFrom?.valueOf() ?? ''),
                )
                updateParam(
                    SEARCH_PARAMS.createToTime,
                    String(startTo?.endOf('day').valueOf() ?? ''), // End of the day
                )
                return searchParams
            })
        } else {
            searchParams.delete(SEARCH_PARAMS.createFromTime)
            searchParams.delete(SEARCH_PARAMS.createToTime)
        }
    }

    const updateParam = (param: string, value?: string) => {
        if (value === 'all' || value === 'Invalid Date' || !value) {
            searchParams.delete(param)
        } else {
            searchParams.set(param, value)
        }
    }

    const handleSearch = (value: string) => {
        const newSearchParams = new URLSearchParams(searchParams)
        if (value) {
            newSearchParams.set(SEARCH_PARAMS.keyword, value)
            newSearchParams.set(SEARCH_PARAMS.searchType, 'TITLE')
        } else {
            newSearchParams.delete(SEARCH_PARAMS.keyword)
            newSearchParams.delete(SEARCH_PARAMS.searchType)
        }
        newSearchParams.set(SEARCH_PARAMS.pageNum, '1')
        setSearchParams(newSearchParams)
    }

    // 将 useEffect 移动到这里
    useEffect(() => {
        handleSearch(debouncedKeyword)
    }, [debouncedKeyword])

    return (
        <section className="flex h-[42px] justify-between">
            <div className="flex items-center bg-bg-accent py-1">
                <Select
                    className="list-search-select"
                    defaultValue="Title"
                    onChange={(value) => {
                        setKeyword('')
                        setTimeout(() => {
                            setSearchType(value)
                        }, 500)
                    }}
                    options={FILTER_OPTIONS}
                    style={{ width: 133 }}
                    suffixIcon={
                        <ReactSVG
                            beforeInjection={(svg) => {
                                svg.classList.add('fill-txt-title')
                            }}
                            src={caretDownIcon}
                        />
                    }
                    value={searchType || 'Title'}
                />
                <div className="h-[22px] w-[1px] rounded bg-txt-inactive" />
                <AppInputBeta
                    onChange={(e) => {
                        setKeyword(e.target.value)
                    }}
                    placeholder="Search"
                    prefixElement={
                        <ReactSVG
                            beforeInjection={(svg) => {
                                svg.classList.add('fill-txt-title')
                            }}
                            src={magnifyingGlassIcon}
                        />
                    }
                    value={keyword}
                    wrapperClassName="w-[345px]"
                />
            </div>
            <div className="flex items-left bg-bg-accent py-1">
                <AppInputDateRangePicker
                    allowClear={false}
                    id="startDatePicker"
                    label="Created Date"
                    onChange={onRefineStartDate}
                    placeholder={['From', 'To']}
                    value={[
                        createFromTimeQuery ? dayjs(Number(createFromTimeQuery)) : undefined,
                        createToTimeQuery ? dayjs(Number(createToTimeQuery)) : undefined,
                    ]}
                />
            </div>
            <div className="flex gap-2">
                <AppButton
                    cosmeticType={adjustedSearchParamSize > 0 ? 'tertiary' : 'quaternary'}
                    label="Refine"
                    onClick={() => {
                        console.log('Refine button clicked')
                        onRefineListClick()
                    }}
                    prefixIcon={
                        <ReactSVG
                            beforeInjection={(svg) => {
                                svg.classList.add('fill-txt-title')
                            }}
                            src={slidersHorizontalIcon}
                        />
                    }
                />
                <ResetRefineryButton resetKeyword={resetKeyword} />
            </div>
        </section>
    )
}

// 删除组件外部的 useEffect