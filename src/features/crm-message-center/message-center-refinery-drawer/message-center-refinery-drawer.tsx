import { useSearchParams } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import {
  App<PERSON>utton,
  AppModal,
  AppInputDateRangePicker,
  ControlledAppSelectInput,
} from '@/features/shared/ui'
import { MESSAGE_CENTER_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import { REFINE_MESSAGE_STATUS_OPTIONS } from '@/features/shared/mappings'

interface MessageCenterRefineryDrawerProps {
  isOpened: boolean
  onClose: () => void
}

interface FormValues {
  status: string
  createDateRange: [string, string] | null
}

export function MessageCenterRefineryDrawer(
  props: MessageCenterRefineryDrawerProps,
) {
  const { onClose } = props
  const [searchParams, setSearchParams] = useSearchParams()

  const statusQuery = searchParams.get(SEARCH_PARAMS.status)
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)

  const { control, handleSubmit, reset } = useForm<FormValues>({
    defaultValues: {
      status: statusQuery || 'all',
      createDateRange:
        createFromTimeQuery && createToTimeQuery
          ? [createFromTimeQuery, createToTimeQuery]
          : null,
    },
  })

  const onSubmit = handleSubmit((data: FormValues) => {
    const newSearchParams = new URLSearchParams(searchParams)

    // Status filter
    if (data.status && data.status !== 'all') {
      newSearchParams.set(SEARCH_PARAMS.status, data.status)
    } else {
      newSearchParams.delete(SEARCH_PARAMS.status)
    }

    // Date range filter
    if (data.createDateRange) {
      newSearchParams.set(SEARCH_PARAMS.createFromTime, data.createDateRange[0])
      newSearchParams.set(SEARCH_PARAMS.createToTime, data.createDateRange[1])
    } else {
      newSearchParams.delete(SEARCH_PARAMS.createFromTime)
      newSearchParams.delete(SEARCH_PARAMS.createToTime)
    }

    newSearchParams.set(SEARCH_PARAMS.pageNum, '1')
    setSearchParams(newSearchParams)
    onClose()
  })

  const onReset = () => {
    reset({
      status: 'all',
      createDateRange: null,
    })
    const newSearchParams = new URLSearchParams(searchParams)
    newSearchParams.delete(SEARCH_PARAMS.status)
    newSearchParams.delete(SEARCH_PARAMS.createFromTime)
    newSearchParams.delete(SEARCH_PARAMS.createToTime)
    newSearchParams.set(SEARCH_PARAMS.pageNum, '1')
    setSearchParams(newSearchParams)
    onClose()
  }

  return (
    <AppModal
    //   isOpened={isOpened}
      onClose={onClose}
      titleLabel="Refine Message List"
      width={400}
    >
      <form className="flex flex-col gap-4" onSubmit={onSubmit}>
        <ControlledAppSelectInput
          control={control}
          label="Status"
          name="status"
          options={REFINE_MESSAGE_STATUS_OPTIONS}
          placeholder="Select status"
        />

        <AppInputDateRangePicker
        //   control={control}
          id="createDateRange"
          label="Created Date Range"
          name="createDateRange"
          placeholder={['Start date', 'End date']}
        />

        <div className="flex gap-2 pt-4">
          <AppButton
            className="flex-1"
            cosmeticType="secondary"
            label="Reset"
            onClick={onReset}
            type="button"
          />
          <AppButton
            className="flex-1"
            cosmeticType="primary"
            label="Apply Filters"
            type="submit"
          />
        </div>
      </form>
    </AppModal>
  )
}