import { useRecoilState } from 'recoil'
import { researchGuidelineDocumentsDetailState } from '@/features/shared/states'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/features/shared/utils'
import { AppModalConfirmation } from '@/features/shared/ui'
import {
  type ResearchGuidelineDocumentsDetailActionType,
  CRMResearchGuidelineDocumentsServices,
} from '@/features/shared/services'

export function ResearchGuidelineDocumentsDeleteModal() {
  const queryClient = useQueryClient()

  const [{ mode, selectedRecord }, setResearchGuidelineDocumentsDetailState] =
    useRecoilState(researchGuidelineDocumentsDetailState)
  const { showToast } = useToast()

  const mutateEditResearchGuidelineDocument = useMutation({
    mutationFn:
      CRMResearchGuidelineDocumentsServices.editResearchGuidelineDocument,
    onError: (error) => {
      showToast('Document Delete Failed', error.message, 'danger')
    },
    onSuccess: () => {
      onCancel()
      queryClient.invalidateQueries({
        queryKey: ['research-guideline-documents-list'],
      })
      showToast(
        'Document Delete',
        `You have deleted a document - ${selectedRecord?.title}`,
        'success',
      )
    },
  })
  const onConfirm = () => {
    if (selectedRecord) {
      const payload = {
        action: mode! as ResearchGuidelineDocumentsDetailActionType,
        documentId: selectedRecord.documentId,
        fileName: selectedRecord.fileName,
        fileType: selectedRecord.fileType,
        fileKey: selectedRecord.fileKey,
      }
      mutateEditResearchGuidelineDocument.mutateAsync(payload)
    }
  }
  const onCancel = () => {
    setResearchGuidelineDocumentsDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedRecord: undefined,
    }))
  }

  return (
    <AppModalConfirmation
      confirmBtnText="Delete Document"
      isLoading={mutateEditResearchGuidelineDocument.isPending}
      isOpened
      modalTitle="Delete Document"
      modalWidth={500}
      onCancel={onCancel}
      onConfirm={onConfirm}
      requiresPassword={false}
    >
      <div>
        <p className="mb-1 text-xs font-light leading-4">
          You are about to delete document -{' '}
          <span className="font-semibold">{selectedRecord?.title}</span>.
        </p>
        <p className="text-xs font-light leading-4">
          This action is irreversible. Would you like to proceed?
        </p>
      </div>
    </AppModalConfirmation>
  )
}
