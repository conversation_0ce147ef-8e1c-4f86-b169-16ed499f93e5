import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  CRMResearchGuidelineDocumentsServices,
  RESEARCH_GUIDELINE_DOCUMENTS_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetResearchGuidelineDocumentsListResponse,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
} from '@/features/shared/ui/app-icons'

const EDIT_PERMISSION = 1800200
const DELETE_PERMISSION = 1800300

interface ResearchGuidelineDocumentsListTableProps {
  onRowClick: (
    record: GetResearchGuidelineDocumentsListResponse,
    action: 'EDIT' | 'DELETE',
  ) => void
}

export function ResearchGuidelineDocumentsListTable(
  props: ResearchGuidelineDocumentsListTableProps,
) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()

  const [searchParams] = useSearchParams()
  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createdFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createdToTime)
  const updateFromTimeQuery = searchParams.get(SEARCH_PARAMS.updatedFromTime)
  const updateToTimeQuery = searchParams.get(SEARCH_PARAMS.updatedToTime)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      CRMResearchGuidelineDocumentsServices.getResearchGuidelineDocumentsList({
        createdFromTime: createFromTimeQuery
          ? dayjs(Number(createFromTimeQuery)).valueOf()
          : 0,
        createdToTime: createToTimeQuery
          ? dayjs(Number(createToTimeQuery)).valueOf()
          : 0,
        keyword: keywordQuery || '',
        pageNum: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        searchType: keywordQuery ? searchTypeQuery || 'DOCUMENT_ID' : '',
        sortBy: sortByQuery || 'CREATED_TIME',
        sortType: sortTypeQuery || 'DESC',
        updatedFromTime: updateFromTimeQuery
          ? dayjs(Number(updateFromTimeQuery)).valueOf()
          : 0,
        updatedToTime: updateToTimeQuery
          ? dayjs(Number(updateToTimeQuery)).valueOf()
          : 0,
      }),
    queryKey: [
      'research-guideline-documents-list',
      keywordQuery,
      searchTypeQuery,
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
      createFromTimeQuery,
      createToTimeQuery,
      updateFromTimeQuery,
      updateToTimeQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const columns: TableProps<GetResearchGuidelineDocumentsListResponse>['columns'] =
    [
      {
        dataIndex: 'documentId',
        key: 'documentId',
        title: 'Document ID',
      },
      {
        dataIndex: 'title',
        key: 'title',
        title: 'Title',
      },
      {
        dataIndex: 'createdTime',
        key: 'CREATED_TIME',
        render: (createTime: string) => (
          <span>
            {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
          </span>
        ),
        sortOrder:
          sortByQuery === 'CREATED_TIME'
            ? convertSortTypeToSortOrder(sortTypeQuery)
            : undefined,
        sorter: true,
        title: 'Created Date',
      },
      {
        dataIndex: 'updatedTime',
        key: 'UPDATED_TIME',
        render: (updateTime: string) => (
          <span>
            {updateTime ? dayjs(updateTime).format('DD-MM-YYYY HH:mm') : '--'}
          </span>
        ),
        sortOrder:
          sortByQuery === 'UPDATED_TIME'
            ? convertSortTypeToSortOrder(sortTypeQuery)
            : undefined,
        sorter: true,
        title: 'Updated Date',
      },
      {
        dataIndex: 'documentId',
        key: 'documentId',
        render: (documentId: number, record) => (
          <div>
            {hasPermission(EDIT_PERMISSION) ||
            hasPermission(DELETE_PERMISSION) ? (
              <Popover
                arrow={false}
                content={
                  <div className="flex flex-col">
                    {hasPermission(EDIT_PERMISSION) && (
                      <AppButton
                        className="justify-start py-2 font-normal"
                        cosmeticType="transparent"
                        label="Edit/View"
                        onClick={(e) => {
                          e.stopPropagation()
                          onRowClick(record, 'EDIT')
                          setSelectedPopover(null)
                        }}
                        prefixIcon={editIcon}
                        type="button"
                      />
                    )}

                    {hasPermission(DELETE_PERMISSION) && (
                      <AppButton
                        className="justify-start py-2 font-normal"
                        cosmeticType="transparent"
                        label="Delete"
                        onClick={(e) => {
                          e.stopPropagation()
                          onRowClick(record, 'DELETE')
                          setSelectedPopover(null)
                        }}
                        prefixIcon={trashIcon}
                        type="button"
                      />
                    )}
                  </div>
                }
                onOpenChange={(newOpen) => {
                  handleOpenChange(newOpen, documentId)
                }}
                open={selectedPopover === documentId}
                placement="bottomRight"
                trigger="click"
              >
                <AppButton
                  cosmeticType="transparent"
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                  prefixIcon={
                    <ReactSVG
                      beforeInjection={(svg) => {
                        svg.classList.add('fill-txt-title')
                      }}
                      src={dotsThreeVerticalIcon}
                    />
                  }
                  type="button"
                />
              </Popover>
            ) : null}
          </div>
        ),
        title: ' ',
        width: 40,
      },
    ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="leadId"
      total={data?.total}
    />
  )
}
