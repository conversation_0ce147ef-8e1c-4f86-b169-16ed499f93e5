import { useNavigate } from 'react-router-dom'
import { useSetRecoilState } from 'recoil'
import { researchGuidelineDocumentsDetailState } from '@/features/shared/states'
import { AppButton, ContentHeader } from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'

const ADD_PERMISSION = 1800100

export function ResearchGuidelineDocumentsContentHeader() {
  const navigate = useNavigate()
  const { hasPermission } = usePermission()

  const seResearchGuidelineDocumentsDetail = useSetRecoilState(
    researchGuidelineDocumentsDetailState,
  )

  const onBack = () => {
    navigate('/admin-panel/crm/customer-relations/list')
  }

  const openResearchGuidelineDocumentsModal = () => {
    seResearchGuidelineDocumentsDetail({
      isOnboardingCreate: true,
      mode: 'ADD',
    })
  }

  return (
    <ContentHeader onBack={onBack} title="Research & Guideline Documents">
      {hasPermission(ADD_PERMISSION) && (
        <AppButton
          className="mr-2"
          label="Add Document"
          onClick={openResearchGuidelineDocumentsModal}
          prefixIcon={plusGreenIcon}
        />
      )}
    </ContentHeader>
  )
}
