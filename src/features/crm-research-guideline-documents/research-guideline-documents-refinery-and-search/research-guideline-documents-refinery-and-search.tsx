import Select, { type BaseOptionType } from 'antd/es/select'
import { useSearchParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { useState, useEffect } from 'react'
import { useDebounceValue } from 'usehooks-ts'
import { AppButton } from '@/features/shared/ui'
import { AppInputBeta } from '@/features/shared/ui/app-input-beta'
import {
  arrowClockwiseIcon,
  caretDownIcon,
  magnifyingGlassIcon,
  slidersHorizontalIcon,
} from '@/features/shared/ui/app-icons'

const FILTER_OPTIONS: BaseOptionType[] = [
  {
    label: 'Document ID',
    value: 'DOCUMENT_ID',
  },
  {
    label: 'Title',
    value: 'TITLE',
  },
]
interface ResetRefineryButtonProps {
  resetKeyword: () => void
}
function ResetRefineryButton({ resetKeyword }: ResetRefineryButtonProps) {
  const [, setSearchParams] = useSearchParams()

  const resetRefinery = () => {
    setSearchParams(() => {
      return {}
    })
    resetKeyword()
  }

  return (
    <AppButton
      label="Reset"
      onClick={resetRefinery}
      prefixIcon={
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('fill-txt-title')
          }}
          src={arrowClockwiseIcon}
        />
      }
    />
  )
}

interface CustomerRelationsRefineryAndSearchProps {
  onRefineListClick: () => void
}

export function ResearchGuidelineDocumentsRefineryAndSearch(
  props: CustomerRelationsRefineryAndSearchProps,
) {
  const { onRefineListClick } = props
  const [searchParams, setSearchParams] = useSearchParams()
  const keywordQuery = searchParams.get('keyword')
  const [keyword, setKeyword] = useState(keywordQuery || '')
  const [debouncedKeyword, setDebouncedKeyword] = useDebounceValue(
    keyword || '',
    300,
  )
  const searchTypeQuery = searchParams.get('searchType')
  const [searchType, setSearchType] = useState(searchTypeQuery)
  const adjustedSearchParamSize = searchParams.get('searchType')
    ? searchParams.size - 1
    : searchParams.size

  const resetKeyword = () => {
    setKeyword('')
  }

  useEffect(() => {
    setDebouncedKeyword(keyword)
  }, [keyword, setDebouncedKeyword])

  useEffect(() => {
    setSearchParams((prev) => {
      if (debouncedKeyword) {
        prev.set('keyword', debouncedKeyword)
        searchParams.set('pageNum', '1')
      } else {
        prev.delete('keyword')
      }
      return prev
    })
  }, [debouncedKeyword, setSearchParams])

  useEffect(() => {
    setSearchParams((prev) => {
      if (searchType) prev.set('searchType', searchType)
      else prev.delete('searchType')
      return prev
    })
  }, [searchType, setSearchParams])

  return (
    <section className="flex h-[42px] justify-between">
      <div className="flex items-center bg-bg-accent py-1">
        <Select
          className="list-search-select"
          defaultValue="DOCUMENT_ID"
          onChange={(value) => {
            setKeyword('')
            setTimeout(() => {
              setSearchType(value)
            }, 500)
          }}
          options={FILTER_OPTIONS}
          style={{ width: 133 }}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={caretDownIcon}
            />
          }
          value={searchType || 'DOCUMENT_ID'}
        />
        <div className="h-[22px] w-[1px] rounded bg-txt-inactive" />
        <AppInputBeta
          onChange={(e) => {
            setKeyword(e.target.value)
          }}
          placeholder="Search"
          prefixElement={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={magnifyingGlassIcon}
            />
          }
          value={keyword}
          wrapperClassName="w-[345px]"
        />
      </div>
      <div className="flex gap-2">
        <AppButton
          cosmeticType={adjustedSearchParamSize > 0 ? 'tertiary' : 'quaternary'}
          label="Refine"
          onClick={onRefineListClick}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={slidersHorizontalIcon}
            />
          }
        />
        <ResetRefineryButton resetKeyword={resetKeyword} />
      </div>
    </section>
  )
}
