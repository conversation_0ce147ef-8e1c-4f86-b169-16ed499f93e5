import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { clsx } from 'clsx'
import { useRecoilState } from 'recoil'
import { useForm } from 'react-hook-form'
import { useState, useEffect, useMemo } from 'react'
import { ReactSVG } from 'react-svg'
import { useQuery } from '@tanstack/react-query'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { pdfjs, Document, Page } from 'react-pdf'
import { useToast, useDownload } from '@/features/shared/utils'
import { downloadIcon } from '@/features/shared/ui/app-icons'
import {
  AppButton,
  AppNormalInput,
  RequiredIndicator,
  AppFileUploadInput,
} from '@/features/shared/ui'
import {
  type ResearchGuidelineDocumentItem,
  type EditResearchGuidelineDocumentRequest,
  CommonServices,
  CRMResearchGuidelineDocumentsServices,
} from '@/features/shared/services'
import { researchGuidelineDocumentsDetailState } from '@/features/shared/states'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

const schema = yup
  .object()
  .shape({
    title: yup.string().required('Document Title is a required field'),
    fileKey: yup.string().required('Upload File is a required field'),
  })
  .required()

export type FormValues = yup.InferType<typeof schema>

interface ResearchGuidelineDocumentsAddFormProps {
  onClose: () => void
  selectedRecord: ResearchGuidelineDocumentItem | undefined
}

export function ResearchGuidelineDocumentsAddEditForm(
  props: ResearchGuidelineDocumentsAddFormProps,
) {
  const { onClose, selectedRecord } = props
  const { showToast } = useToast()
  const { downloadFile } = useDownload()
  const [{ mode }, seResearchGuidelineDocumentsDetail] = useRecoilState(
    researchGuidelineDocumentsDetailState,
  )

  const queryClient = useQueryClient()
  const [numPages, setNumPages] = useState(0)
  const [documentFile, setDocumentFile] = useState<File>()
  const [documentId, setDocumentId] = useState<number | undefined>(undefined)

  const handleDownload = (
    file: File | Blob,
    fileName: string,
    fileType?: string,
  ) => {
    if (!fileType) return
    downloadFile({
      file,
      fileExtension: `.${fileType}`,
      filename: fileName,
    })
  }

  const {
    formState: { errors, isValid },
    handleSubmit,
    register,
    reset,
    setValue,
    watch,
  } = useForm<FormValues>({
    defaultValues: {
      title: '',
      fileKey: '',
    },
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })

  const watchedDocumentTitle = watch('title')
  const watchedFileKey = watch('fileKey')

  useEffect(() => {
    if (selectedRecord) {
      setDocumentId(selectedRecord.documentId)
      setValue('title', selectedRecord.title, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      })
      setValue('fileKey', selectedRecord.fileKey, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      })
    }
  }, [selectedRecord])

  const existingFileKey = selectedRecord?.fileKey
  const existingFileName = selectedRecord?.fileName
  const existingFileType = selectedRecord?.fileType.split('/').pop() || ''
  const documentFileType = documentFile?.type.split('/').pop() || ''

  const { data: existingFile, isLoading: isLoadingExistingFile } = useQuery({
    enabled: Boolean(existingFileKey),
    queryFn: () => CommonServices.getFile(existingFileKey!),
    queryKey: ['getFile', existingFileKey],
  })

  const getFileExtension = (mimeType: string) => {
    switch (mimeType) {
      case 'vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'docx'
      case 'vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return 'xlsx'
      default:
        return mimeType
    }
  }

  const existingFileURL = useMemo(
    () => (existingFile ? URL.createObjectURL(existingFile) : ''),
    [existingFile],
  )

  const documentFileRUL = useMemo(
    () => (documentFile ? URL.createObjectURL(documentFile) : ''),
    [documentFile],
  )

  const onCancel = () => {
    reset()
    onClose()
  }

  const mutateEditResearchGuidelineDocument = useMutation({
    mutationFn:
      CRMResearchGuidelineDocumentsServices.editResearchGuidelineDocument,
    onError: (error) => {
      mode === 'ADD'
        ? showToast('Document Creation Failed', error.message, 'danger')
        : showToast('Document Updation Failed', error.message, 'danger')
    },
    onSuccess: () => {
      mode === 'ADD'
        ? showToast(
            'Document Created',
            `You have created a document - ${watchedDocumentTitle}`,
            'success',
          )
        : showToast(
            'Document Updated',
            `You have updated a document - ${watchedDocumentTitle}`,
            'success',
          )

      queryClient.invalidateQueries({
        queryKey: ['research-guideline-documents-list'],
      })
      onClose()
    },
  })

  const __onSubmit = (formValues: FormValues) => {
    const payload: EditResearchGuidelineDocumentRequest = {
      action: mode!,
      documentId: documentId,
      title: formValues.title,
      fileName: documentFile?.name || selectedRecord?.fileName || '',
      fileKey: formValues.fileKey,
      fileType: documentFile?.type || selectedRecord?.fileType || '',
    }
    mutateEditResearchGuidelineDocument.mutateAsync(payload)
  }

  const renderFileContent = () => {
    const fileTypes = {
      image: ['png', 'jpg', 'jpeg'],
      pdf: ['pdf'],
    }

    if (
      fileTypes.image.includes(existingFileType) ||
      fileTypes.image.includes(documentFileType)
    ) {
      return (
        <img
          alt="thumbnail"
          className="max-h-full max-w-full object-contain"
          src={documentFileRUL || existingFileURL}
        />
      )
    } else if (
      fileTypes.pdf.includes(existingFileType) ||
      fileTypes.pdf.includes(documentFileType)
    ) {
      return (
        <Document
          file={documentFileRUL || existingFileURL}
          onLoadSuccess={({ numPages }) => {
            setNumPages(numPages)
          }}
        >
          <div className="flex max-h-[600px] flex-col items-center overflow-y-auto overflow-x-hidden">
            {[...Array(numPages)].map((_, index) => (
              <Page
                key={`upload_page_${index + 1}`}
                pageNumber={index + 1}
                width={832}
              />
            ))}
          </div>
        </Document>
      )
    } else if (mode !== 'ADD' && !isLoadingExistingFile && watchedFileKey) {
      return (
        <p>
          Preview not available. Please use the download button to view the
          document.
        </p>
      )
    }
  }

  return (
    <form className="flex flex-col gap-3" onSubmit={handleSubmit(__onSubmit)}>
      <section
        className={clsx(
          'grid gap-3',
          mode === 'ADD' ? 'grid-cols-1' : 'grid-cols-2',
        )}
      >
        <AppNormalInput
          label="Document Title"
          {...register('title')}
          error={errors.title?.message}
          placeholder="Input document title"
          required
        />
        <AppFileUploadInput
          accept=".jpg, .jpeg, .png, .pdf, .docx, .xlsx"
          label="Upload File"
          {...register('fileKey')}
          information="Max size of 30 MB, accepted file format: pdf, jpeg, jpg, png, docx or xlsx"
          maxFileSize={30}
          onChange={(fileKey, file) => {
            if (mode === 'EDIT') {
              seResearchGuidelineDocumentsDetail((prev) => ({
                ...prev,
                selectedRecord: undefined,
              }))
            }
            setDocumentFile(file)
            setValue('fileKey', fileKey, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true,
            })
          }}
          placeholder="upload file ..."
          value={documentFile?.name || selectedRecord?.fileName || ''}
          required
        />
      </section>

      <div
        className={clsx(
          'flex w-full items-center justify-center rounded bg-white',
          mode === 'ADD' ? 'h-[300px]' : 'h-[600px]',
        )}
      >
        {/* Loading State */}
        {isLoadingExistingFile ? (
          <span className="font-light text-txt-inactive">Loading File...</span>
        ) : (
          mode === 'EDIT' && renderFileContent()
        )}
      </div>

      <RequiredIndicator />

      <section
        className={clsx(
          'sticky bottom-0 left-0 right-0 top-0 z-50 grid gap-2 bg-white py-6',
          selectedRecord ? 'grid-cols-3' : 'grid-cols-2',
        )}
      >
        <AppButton cosmeticType="skeleton" label="Cancel" onClick={onCancel} />
        {existingFile && existingFileName ? (
          <AppButton
            cosmeticType="quaternary"
            label="Download This Document"
            onClick={() => {
              handleDownload(
                existingFile,
                existingFileName,
                getFileExtension(existingFileType),
              )
            }}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-paragraph')
                }}
                src={downloadIcon}
              />
            }
          />
        ) : null}
        <AppButton
          cosmeticType="secondary"
          disabled={!isValid}
          label={mode === 'ADD' ? 'Create' : 'Save Changes'}
          isLoading={mutateEditResearchGuidelineDocument.isPending}
          type="submit"
        />
      </section>
    </form>
  )
}
