import { useSetRecoilState } from 'recoil'
import { ReactSVG } from 'react-svg'
import { researchGuidelineDocumentsDetailState } from '@/features/shared/states'
import { AppButton } from '@/features/shared/ui'
import { schoolIcon } from '@/features/shared/ui/app-icons'

export function ResearchGuidelineDocumentsAddOnboarding() {
  const setResearchGuidelineDocumentsDetailState = useSetRecoilState(
    researchGuidelineDocumentsDetailState,
  )

  const handleStart = () => {
    setResearchGuidelineDocumentsDetailState((prev) => ({
      ...prev,
      isOnboardingCreate: false,
    }))
  }

  return (
    <section className="flex flex-col items-center gap-6 px-[60px] pb-[84px] pt-[20px]">
      <div className="grid h-[150px] w-[150px] place-items-center rounded-full bg-bg-info">
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('fill-txt-label', 'w-[72px]', 'h-[72px]')
          }}
          src={schoolIcon}
        />
      </div>
      <div className="space-y-2">
        <h4 className="text-center text-sm font-semibold leading-[14px] text-txt-title">
          Adding Documents
        </h4>

        <p className="text-xs font-light leading-4 text-txt-paragraph">
          Here, you can upload new research materials for asset management.
        </p>
        <p className="text-xs font-light leading-4 text-txt-paragraph">
          Just ensure your title is clear and accurately reflects the content,
          and attach the document in PDF, PNG, or JPEG format.
        </p>
      </div>

      <AppButton
        className="w-full"
        cosmeticType="secondary"
        label="Start"
        onClick={handleStart}
      />
    </section>
  )
}
