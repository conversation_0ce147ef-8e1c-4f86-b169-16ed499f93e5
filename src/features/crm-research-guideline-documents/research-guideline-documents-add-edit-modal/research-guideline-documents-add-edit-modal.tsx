import { useRecoilState } from 'recoil'
import { AppModal } from '@/features/shared/ui'
import { researchGuidelineDocumentsDetailState } from '@/features/shared/states'

import {
  ResearchGuidelineDocumentsAddEditForm,
  ResearchGuidelineDocumentsAddOnboarding,
} from './ui'

export function ResearchGuidelineDocumentsAddEditModal() {
  const [
    { mode, selectedRecord, isOnboardingCreate },
    seResearchGuidelineDocumentsDetail,
  ] = useRecoilState(researchGuidelineDocumentsDetailState)

  const onClose = () => {
    seResearchGuidelineDocumentsDetail((prev) => ({
      ...prev,
      mode: undefined,
    }))
  }

  let titleLabel = ''

  if (isOnboardingCreate) {
    titleLabel = ''
  } else if (mode === 'ADD') {
    titleLabel = 'Add Document'
  } else {
    titleLabel = 'Edit/View'
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={onClose}
      open
      titleLabel={titleLabel}
      width={mode === 'ADD' ? 500 : 880}
    >
      {isOnboardingCreate ? (
        <ResearchGuidelineDocumentsAddOnboarding />
      ) : (
        <ResearchGuidelineDocumentsAddEditForm
          onClose={onClose}
          selectedRecord={selectedRecord}
        />
      )}
    </AppModal>
  )
}
