import { DatePicker } from 'antd'
import { useRef, useState, useEffect } from 'react'
import { ReactSVG } from 'react-svg'
import type { PickerRef } from 'rc-picker'
import dayjs, { type Dayjs } from 'dayjs'
import { useSearchParams } from 'react-router-dom'
import { AppButton, AppRefineryDrawer } from '@/features/shared/ui'
import {
  arrowHorizontalLeftIcon,
  arrowHorizontalRightIcon,
  arrowsHorizontalIcon,
  calendarBlankIcon,
} from '@/features/shared/ui/app-icons'
import { parseTimeQueryParam } from '@/features/shared/utils'
import { RESEARCH_GUIDELINE_DOCUMENTS_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
interface TransactionListRefineryDrawerProps {
  isOpened: boolean
  onClose: () => void
}
interface RefineryDatePickerProps {
  label: string
  fromTime: Dayjs | null
  toTime: Dayjs | null
  setFromTime: (date: Dayjs) => void
  setToTime: (date: Dayjs) => void
  setToggle: (mode: string) => void
  toggleMode: string
}

function RefineryDatePicker(props: RefineryDatePickerProps) {
  const {
    fromTime,
    toTime,
    setFromTime,
    setToTime,
    setToggle,
    label,
    toggleMode,
  } = props
  const [dateFilterMode, setDateFilterMode] = useState<'all' | 'from' | 'to'>(
    'all',
  )

  useEffect(() => {
    setDateFilterMode(toggleMode as 'all' | 'from' | 'to')
  }, [toggleMode])

  const fromDateRef = useRef<PickerRef>(null)
  const toDateRef = useRef<PickerRef>(null)

  const toggleDateFilterMode = () => {
    if (dateFilterMode === 'all') {
      setDateFilterMode('from')
      setToggle('from')
    } else if (dateFilterMode === 'from') {
      setDateFilterMode('to')
      setToggle('to')
    } else {
      setDateFilterMode('all')
      setToggle('all')
    }
  }

  const onFocusDateFields = () => {
    if (dateFilterMode === 'from' || dateFilterMode === 'all') {
      fromDateRef.current?.nativeElement.click()
    } else {
      toDateRef.current?.nativeElement.click()
    }
  }

  return (
    <div className="mt-3">
      <h3 className="text-xs font-semibold not-italic">{label}</h3>
      <div className="mt-2 grid grid-cols-[1fr,auto,1fr,auto] items-center gap-2">
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'to'}
          format="DD-MM-YYYY"
          maxDate={toTime || undefined}
          onChange={(date: Dayjs) => {
            setFromTime(date)
            if (dateFilterMode === 'all') {
              toDateRef.current?.nativeElement.click()
            }
          }}
          placeholder="DD-MM-YYYY"
          ref={fromDateRef}
          suffixIcon={null}
          value={fromTime}
        />
        <AppButton
          className="rounded-sm"
          cosmeticType="tertiary"
          onClick={toggleDateFilterMode}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={(() => {
                if (dateFilterMode === 'all') {
                  return arrowsHorizontalIcon
                } else if (dateFilterMode === 'from') {
                  return arrowHorizontalLeftIcon
                }
                return arrowHorizontalRightIcon
              })()}
            />
          }
        />
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'from'}
          format="DD-MM-YYYY"
          minDate={fromTime || undefined}
          onChange={(date: Dayjs) => {
            const endOfDayTime = date.endOf('day')
            setToTime(endOfDayTime)
          }}
          placeholder="DD-MM-YYYY"
          ref={toDateRef}
          suffixIcon={null}
          value={toTime}
        />
        <AppButton
          className="rounded-sm p-3"
          cosmeticType="quaternary"
          onClick={onFocusDateFields}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={calendarBlankIcon}
            />
          }
        />
      </div>
    </div>
  )
}

export function ResearchGuidelineDocumentsRefineryDrawer(
  props: TransactionListRefineryDrawerProps,
) {
  const { isOpened, onClose } = props

  const [searchParams, setSearchParams] = useSearchParams()
  const createdFromTimeQuery = searchParams.get(SEARCH_PARAMS.createdFromTime)
  const createdToTimeQuery = searchParams.get(SEARCH_PARAMS.createdToTime)
  const [createdFromTime, setCreatedFromTime] = useState(
    parseTimeQueryParam(createdFromTimeQuery),
  )
  const [createdToTime, setCreatedToTime] = useState(
    parseTimeQueryParam(createdToTimeQuery),
  )
  const updatedFromTimeQuery = searchParams.get(SEARCH_PARAMS.updatedFromTime)
  const updatedToTimeQuery = searchParams.get(SEARCH_PARAMS.updatedToTime)
  const [updatedFromTime, setUpdatedFromTime] = useState(
    parseTimeQueryParam(updatedFromTimeQuery),
  )
  const [updatedToTime, setUpdatedToTime] = useState(
    parseTimeQueryParam(updatedToTimeQuery),
  )

  const [createdToggle, setCreatedToggle] = useState('all')
  const [updatedToggle, setUpdatedToggle] = useState('all')

  const onReset = () => {
    setCreatedFromTime(null)
    setCreatedToTime(null)
    setUpdatedFromTime(null)
    setUpdatedToTime(null)
    setCreatedToggle('all')
    setUpdatedToggle('all')
  }

  const onApply = () => {
    const updateParam = (param: string, value?: string) => {
      if (value === 'all' || value === 'Invalid Date' || !value) {
        searchParams.delete(param)
      } else {
        searchParams.set(param, value)
        searchParams.set('pageNum', '1')
      }
    }

    setSearchParams((searchParams) => {
      updateParam(
        SEARCH_PARAMS.createdFromTime,
        String(
          createdToggle === 'to'
            ? 'all'
            : dayjs(createdFromTime).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.createdToTime,
        String(
          createdToggle === 'from'
            ? 'all'
            : dayjs(createdToTime).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.updatedFromTime,
        String(
          updatedToggle === 'to'
            ? 'all'
            : dayjs(updatedFromTime).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.updatedToTime,
        String(
          updatedToggle === 'from'
            ? 'all'
            : dayjs(updatedToTime).valueOf() || 'all',
        ),
      )
      return searchParams
    })
    onClose()
  }

  // refresh all queries on open
  useEffect(() => {
    setCreatedFromTime(parseTimeQueryParam(Number(createdFromTimeQuery)))
    setCreatedToTime(parseTimeQueryParam(Number(createdToTimeQuery)))
    setUpdatedFromTime(parseTimeQueryParam(Number(updatedFromTimeQuery)))
    setUpdatedToTime(parseTimeQueryParam(Number(updatedToTimeQuery)))
  }, [isOpened, searchParams])

  return (
    <AppRefineryDrawer
      isOpened={isOpened}
      onApply={onApply}
      onCancel={onClose}
      onClose={onClose}
      onReset={onReset}
    >
      <RefineryDatePicker
        fromTime={createdFromTime}
        label="Created Date"
        setFromTime={setCreatedFromTime}
        setToTime={setCreatedToTime}
        setToggle={setCreatedToggle}
        toTime={createdToTime}
        toggleMode={createdToggle}
      />
      <RefineryDatePicker
        fromTime={updatedFromTime}
        label="Updated Date"
        setFromTime={setUpdatedFromTime}
        setToTime={setUpdatedToTime}
        setToggle={setUpdatedToggle}
        toTime={updatedToTime}
        toggleMode={updatedToggle}
      />
    </AppRefineryDrawer>
  )
}
