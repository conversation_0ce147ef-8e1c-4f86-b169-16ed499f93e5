import { ReactSVG } from 'react-svg'
import { AppBreadcrumb } from '@/features/shared/ui'
import {
  arrowsLeftRightIcon,
  listDashesIcon,
} from '@/features/shared/ui/app-icons'

export function CustomerRelationsBreadcrumbs() {
  return (
    <div className="mb-4">
      <AppBreadcrumb
        breadcrumbs={[
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-label')
                }}
                src={arrowsLeftRightIcon}
              />
            ),
            label: 'Customer Relationship',
          },
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={listDashesIcon}
              />
            ),
            label: 'Customer Relations',
          },
        ]}
      />
    </div>
  )
}
