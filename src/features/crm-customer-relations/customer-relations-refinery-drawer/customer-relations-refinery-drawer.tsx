import { DatePicker } from 'antd'
import { useRef, useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { ReactSVG } from 'react-svg'
import type { PickerRef } from 'rc-picker'
import { clsx } from 'clsx'
import { useQuery } from '@tanstack/react-query'
import dayjs, { type Dayjs } from 'dayjs'
import { useSearchParams } from 'react-router-dom'
import {
  AppButton,
  AppRefineryDrawer,
  ControlledAppSelectInput,
} from '@/features/shared/ui'
import {
  arrowHorizontalLeftIcon,
  arrowHorizontalRightIcon,
  arrowsHorizontalIcon,
  calendarBlankIcon,
} from '@/features/shared/ui/app-icons'
import { parseTimeQueryParam } from '@/features/shared/utils'
import { CUSTOMER_RELATIONS_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import {
  REFINE_LEAD_STATUS_OPTIONS,
  REFINE_LEAD_TYPE_OPTIONS,
  REFINE_LEAD_ASSIGNED_RM,
  REFINE_LEAD_PRIORITY_OPTIONS,
} from '@/features/shared/mappings'

import {
  CommonServices,
  type GetEnumerationResponse,
} from '@/features/shared/services'

const getOptions = (
  code: string,
  enumeration?: GetEnumerationResponse,
): { label: string; value: string }[] | undefined => {
  return enumeration?.list
    .find((x) => x.code === code)
    ?.list.map((x) => ({
      label: x.desc,
      value: x.value,
    }))
}

const schema = yup
  .object()
  .shape({
    countryRegion: yup.string(),
  })
  .required()

export type FormValues = yup.InferType<typeof schema>

interface TransactionListRefineryDrawerProps {
  isOpened: boolean
  onClose: () => void
}

interface RefineSectionProps {
  title: string
  titleIcon?: string
  refineList: {
    label: string
    labelColor?: string
    value: string
    suffixIcon?: string
    indicatorColor?: string
    prefixIcon?: string
  }[]
  activeValue: string
  onClick: (value: string) => void
  cols?: number
}

function RefineSection(props: RefineSectionProps) {
  const { title, titleIcon, refineList, activeValue, onClick, cols } = props
  return (
    <div className="mt-3 first-of-type:mt-6">
      <div className="flex items-center gap-0.5">
        <h3 className="text-xs font-semibold not-italic">{title}</h3>
        {titleIcon ? <img alt="icon" src={titleIcon} /> : null}
      </div>
      <div
        className={clsx(
          'mt-2 grid gap-1',
          cols === 2 && 'grid-cols-2',
          cols === 4 && 'grid-cols-4',
          !cols && 'grid-cols-3',
        )}
      >
        {refineList.map((item) => (
          <AppButton
            className={`font-normal ${cols === 4 ? 'min-w-[60px]' : ''}`}
            cosmeticType={
              activeValue === item.value ? 'tertiary' : 'quaternary'
            }
            key={item.value}
            label={item.label}
            onClick={() => {
              onClick(item.value)
            }}
            style={{ color: item.labelColor }}
          />
        ))}
      </div>
    </div>
  )
}

interface RefineryDatePickerProps {
  label: string
  fromTime: Dayjs | null
  toTime: Dayjs | null
  setFromTime: (date: Dayjs) => void
  setToTime: (date: Dayjs) => void
  setToggle: (mode: string) => void
  toggleMode: string
}

function RefineryDatePicker(props: RefineryDatePickerProps) {
  const {
    fromTime,
    toTime,
    setFromTime,
    setToTime,
    setToggle,
    label,
    toggleMode,
  } = props
  const [dateFilterMode, setDateFilterMode] = useState<'all' | 'from' | 'to'>(
    'all',
  )

  useEffect(() => {
    setDateFilterMode(toggleMode as 'all' | 'from' | 'to')
  }, [toggleMode])

  const fromDateRef = useRef<PickerRef>(null)
  const toDateRef = useRef<PickerRef>(null)

  const toggleDateFilterMode = () => {
    if (dateFilterMode === 'all') {
      setDateFilterMode('from')
      setToggle('from')
    } else if (dateFilterMode === 'from') {
      setDateFilterMode('to')
      setToggle('to')
    } else {
      setDateFilterMode('all')
      setToggle('all')
    }
  }

  const onFocusDateFields = () => {
    if (dateFilterMode === 'from' || dateFilterMode === 'all') {
      fromDateRef.current?.nativeElement.click()
    } else {
      toDateRef.current?.nativeElement.click()
    }
  }

  return (
    <div className="mt-3">
      <h3 className="text-xs font-semibold not-italic">{label}</h3>
      <div className="mt-2 grid grid-cols-[1fr,auto,1fr,auto] items-center gap-2">
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'to'}
          format={{
            format: 'DD-MM-YYYY',
            type: 'mask',
          }}
          maxDate={toTime || undefined}
          onChange={(date: Dayjs) => {
            setFromTime(date)
            if (dateFilterMode === 'all') {
              toDateRef.current?.nativeElement.click()
            }
          }}
          placeholder="DD-MM-YYYY"
          ref={fromDateRef}
          suffixIcon={null}
          value={fromTime}
        />
        <AppButton
          className="rounded-sm"
          cosmeticType="tertiary"
          onClick={toggleDateFilterMode}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={(() => {
                if (dateFilterMode === 'all') {
                  return arrowsHorizontalIcon
                } else if (dateFilterMode === 'from') {
                  return arrowHorizontalLeftIcon
                }
                return arrowHorizontalRightIcon
              })()}
            />
          }
        />
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'from'}
          format="DD-MM-YYYY"
          minDate={fromTime || undefined}
          onChange={(date: Dayjs) => {
            const endOfDayTime = date.endOf('day')
            setToTime(endOfDayTime)
          }}
          placeholder="DD-MM-YYYY"
          ref={toDateRef}
          suffixIcon={null}
          value={toTime}
        />
        <AppButton
          className="rounded-sm p-3"
          cosmeticType="quaternary"
          onClick={onFocusDateFields}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={calendarBlankIcon}
            />
          }
        />
      </div>
    </div>
  )
}

export function CustomerRelationsRefineryDrawer(
  props: TransactionListRefineryDrawerProps,
) {
  const { isOpened, onClose } = props
  const { data: enumeration } = useQuery({
    queryFn: () =>
      CommonServices.getEnumeration({
        codeList: ['REGION'],
      }),
    queryKey: ['region'],
  })
  const countryListOptions = getOptions('REGION', enumeration)

  const { reset, control } = useForm({
    defaultValues: {
      countryRegion: '',
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })

  const [searchParams, setSearchParams] = useSearchParams()
  const [tempCustomerRelationsStatus, setTempCustomerRelationsStatus] =
    useState(searchParams.get(SEARCH_PARAMS.status) || 'all')
  const [tempCustomerRelationsType, setTempCustomerRelationsType] = useState(
    searchParams.get(SEARCH_PARAMS.type) || 'all',
  )
  const [tempCustomerRelationsAssignedRM, setTempCustomerRelationsAssignedRM] =
    useState(searchParams.get(SEARCH_PARAMS.isMyLead) || 'all')
  const [tempCustomerRelationsPriority, setTempCustomerRelationsPriority] =
    useState(searchParams.get(SEARCH_PARAMS.priority) || 'all')
  const [tempCountryRegion, setTempCountryRegion] = useState(
    searchParams.get(SEARCH_PARAMS.countryRegion) || '',
  )
  const assignedDateFromQuery = searchParams.get(SEARCH_PARAMS.assignedDateFrom)
  const assignedDateToQuery = searchParams.get(SEARCH_PARAMS.assignedDateTo)
  const [assignedDateFrom, setAssignedDateFrom] = useState(
    parseTimeQueryParam(assignedDateFromQuery),
  )
  const [assignedDateTo, setAssignedDateTo] = useState(
    parseTimeQueryParam(assignedDateToQuery),
  )
  const createdDateFromQuery = searchParams.get(SEARCH_PARAMS.createdDateFrom)
  const createdDateToQuery = searchParams.get(SEARCH_PARAMS.createdDateTo)
  const [createdDateFrom, setCreatedDateFrom] = useState(
    parseTimeQueryParam(createdDateFromQuery),
  )
  const [createdDateTo, setCreatedDateTo] = useState(
    parseTimeQueryParam(createdDateToQuery),
  )

  const [createdToggle, setCreatedToggle] = useState('all')
  const [assignedToggle, setAssignedToggle] = useState('all')

  const onReset = () => {
    reset()
    setTempCustomerRelationsStatus('all')
    setTempCustomerRelationsType('all')
    setTempCustomerRelationsAssignedRM('all')
    setTempCustomerRelationsPriority('all')
    setTempCountryRegion('')
    setCreatedToggle('all')
    setAssignedToggle('all')
    setAssignedDateFrom(null)
    setAssignedDateTo(null)
    setCreatedDateFrom(null)
    setCreatedDateTo(null)
  }

  const onApply = () => {
    const updateParam = (param: string, value?: string) => {
      if (value === 'all' || value === 'Invalid Date' || !value) {
        searchParams.delete(param)
      } else {
        searchParams.set(param, value)
        searchParams.set('pageNum', '1')
      }
    }

    setSearchParams((searchParams) => {
      updateParam(SEARCH_PARAMS.status, tempCustomerRelationsStatus)
      updateParam(SEARCH_PARAMS.type, tempCustomerRelationsType)
      updateParam(SEARCH_PARAMS.isMyLead, tempCustomerRelationsAssignedRM)
      updateParam(SEARCH_PARAMS.priority, tempCustomerRelationsPriority)
      updateParam(SEARCH_PARAMS.countryRegion, tempCountryRegion)
      updateParam(
        SEARCH_PARAMS.assignedDateFrom,
        String(
          assignedToggle === 'to'
            ? 'all'
            : dayjs(assignedDateFrom).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.assignedDateTo,
        String(
          assignedToggle === 'from'
            ? 'all'
            : dayjs(assignedDateTo).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.createdDateFrom,
        String(
          createdToggle === 'to'
            ? 'all'
            : dayjs(createdDateFrom).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.createdDateTo,
        String(
          createdToggle === 'from'
            ? 'all'
            : dayjs(createdDateTo).valueOf() || 'all',
        ),
      )
      return searchParams
    })
    onClose()
  }

  // refresh all queries on open
  useEffect(() => {
    const tempCustomerRelationsStatus = searchParams.get(SEARCH_PARAMS.status)
    const tempCustomerRelationsAssignedRM = searchParams.get(
      SEARCH_PARAMS.isMyLead,
    )
    const tempCustomerRelationsPriority = searchParams.get(
      SEARCH_PARAMS.priority,
    )
    setTempCustomerRelationsStatus(tempCustomerRelationsStatus || 'all')
    setTempCustomerRelationsAssignedRM(tempCustomerRelationsAssignedRM || 'all')
    setTempCustomerRelationsPriority(tempCustomerRelationsPriority || 'all')
    setAssignedDateFrom(parseTimeQueryParam(Number(assignedDateFromQuery)))
    setAssignedDateTo(parseTimeQueryParam(Number(assignedDateToQuery)))
    setCreatedDateFrom(parseTimeQueryParam(Number(createdDateFromQuery)))
    setCreatedDateTo(parseTimeQueryParam(Number(createdDateToQuery)))
  }, [isOpened, searchParams])

  return (
    <AppRefineryDrawer
      isOpened={isOpened}
      onApply={onApply}
      onCancel={onClose}
      onClose={onClose}
      onReset={onReset}
    >
      <div>
        <RefineSection
          activeValue={tempCustomerRelationsStatus}
          onClick={(value) => {
            setTempCustomerRelationsStatus(value)
          }}
          refineList={REFINE_LEAD_STATUS_OPTIONS}
          title="Status"
        />
        <RefineSection
          activeValue={tempCustomerRelationsType}
          onClick={(value) => {
            setTempCustomerRelationsType(value)
          }}
          refineList={REFINE_LEAD_TYPE_OPTIONS}
          title="Type"
        />
        <RefineSection
          activeValue={tempCustomerRelationsAssignedRM}
          cols={2}
          onClick={(value) => {
            setTempCustomerRelationsAssignedRM(value)
          }}
          refineList={REFINE_LEAD_ASSIGNED_RM}
          title="Assigned RM"
        />
        <RefineSection
          activeValue={tempCustomerRelationsPriority}
          cols={4}
          onClick={(value) => {
            setTempCustomerRelationsPriority(value)
          }}
          refineList={REFINE_LEAD_PRIORITY_OPTIONS}
          title="Priority"
        />
      </div>
      <div className="mt-3">
        <ControlledAppSelectInput
          control={control}
          label="Country/Region"
          name="countryRegion"
          onChange={(value: string) => {
            setTempCountryRegion(value)
          }}
          optionFilterProp="label"
          options={countryListOptions}
          placeholder="Select country/region"
          showSearch
        />
      </div>
      <RefineryDatePicker
        fromTime={assignedDateFrom}
        label="Assigned Date"
        setFromTime={setAssignedDateFrom}
        setToTime={setAssignedDateTo}
        setToggle={setAssignedToggle}
        toTime={assignedDateTo}
        toggleMode={assignedToggle}
      />
      <RefineryDatePicker
        fromTime={createdDateFrom}
        label="Created Date"
        setFromTime={setCreatedDateFrom}
        setToTime={setCreatedDateTo}
        setToggle={setCreatedToggle}
        toTime={createdDateTo}
        toggleMode={createdToggle}
      />
    </AppRefineryDrawer>
  )
}
