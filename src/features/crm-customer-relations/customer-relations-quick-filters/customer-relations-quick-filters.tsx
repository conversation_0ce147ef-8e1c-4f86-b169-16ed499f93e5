import { useSearchParams } from 'react-router-dom'
import {
  REFINE_LEAD_STATUS_OPTIONS,
  REFINE_LEAD_ASSIGNED_RM,
  REFINE_LEAD_PRIORITY_OPTIONS,
} from '@/features/shared/mappings'
import { AppSwitchButton, Divider } from '@/features/shared/ui'
import { CUSTOMER_RELATIONS_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'

export function CustomerRelationsQuickFilters() {
  const [searchParams, setSearchParams] = useSearchParams()
  const tempCustomerRelationsStatus = searchParams.get(SEARCH_PARAMS.status)
  const tempCustomerRelationsAssignedRM = searchParams.get(
    SEARCH_PARAMS.isMyLead,
  )
  const tempCustomerRelationsPriority = searchParams.get(SEARCH_PARAMS.priority)

  const updateParam = (param: string, value?: string) => {
    if (value === 'all' || value === 'Invalid Date' || !value) {
      searchParams.delete(param)
    } else {
      searchParams.set(param, value)
      searchParams.set('pageNum', '1')
    }
  }

  const changeListStatus = (value: string) => {
    setSearchParams((searchParams) => {
      updateParam(SEARCH_PARAMS.status, value)
      return searchParams
    })
  }

  const changeListAssignedRM = (value: string) => {
    setSearchParams((searchParams) => {
      updateParam(SEARCH_PARAMS.isMyLead, value)
      return searchParams
    })
  }

  const changeListPriority = (value: string) => {
    setSearchParams((searchParams) => {
      updateParam(SEARCH_PARAMS.priority, value)
      return searchParams
    })
  }

  return (
    <div className="flex flex-wrap items-center gap-2">
      <div className="grow">
        <section className="grid grid-cols-[minmax(100px,1fr)_minmax(100px,1fr)_minmax(100px,1fr)]">
          {REFINE_LEAD_STATUS_OPTIONS.map((tab) => (
            <AppSwitchButton
              className="h-10"
              isActive={
                tempCustomerRelationsStatus === tab.value ||
                (tab.value === 'all' && !tempCustomerRelationsStatus)
              }
              key={tab.value}
              onClick={() => {
                changeListStatus(tab.value)
              }}
            >
              {tab.label}
            </AppSwitchButton>
          ))}
        </section>
      </div>
      <Divider />
      <div className="grow">
        <section className="grid grid-cols-[minmax(100px,1fr)_minmax(100px,1fr)]">
          {REFINE_LEAD_ASSIGNED_RM.map((tab) => (
            <AppSwitchButton
              className="h-10"
              isActive={
                tempCustomerRelationsAssignedRM === tab.value ||
                (tab.value === 'all' && !tempCustomerRelationsAssignedRM)
              }
              key={tab.value}
              onClick={() => {
                changeListAssignedRM(tab.value)
              }}
            >
              {tab.label}
            </AppSwitchButton>
          ))}
        </section>
      </div>
      <Divider />
      <div className="grow">
        <section className="grid grid-cols-[minmax(100px,1fr)_minmax(100px,1fr)_minmax(100px,1fr)_minmax(100px,1fr)]">
          {REFINE_LEAD_PRIORITY_OPTIONS.map((tab) => (
            <AppSwitchButton
              className="h-10"
              isActive={
                tempCustomerRelationsPriority === tab.value ||
                (tab.value === 'all' && !tempCustomerRelationsPriority)
              }
              key={tab.value}
              onClick={() => {
                changeListPriority(tab.value)
              }}
            >
              {tab.label}
            </AppSwitchButton>
          ))}
        </section>
      </div>
    </div>
  )
}
