import { useRecoilState } from 'recoil'
import { customerRelationsDetailState } from '@/features/shared/states'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/features/shared/utils'
import { AppModalConfirmation } from '@/features/shared/ui'
import {
  type LeadDetailActionType,
  CRMLeadServices,
} from '@/features/shared/services'

export function CustomerRelationsDeleteModal() {
  const queryClient = useQueryClient()

  const [{ mode, selectedRecord }, seCustomerRelationsDetail] = useRecoilState(
    customerRelationsDetailState,
  )
  const { showToast } = useToast()

  const mutateEditLeadDetail = useMutation({
    mutationFn: CRMLeadServices.deleteLeadDetail,
    onError: (error) => {
      showToast('Lead Delete Failed', error.message, 'danger')
    },
    onSuccess: () => {
      onCancel()
      queryClient.invalidateQueries({
        queryKey: ['customer-relations-list'],
      })
      showToast(
        'Lead Delete',
        `You have deleted a lead - ${selectedRecord?.name}`,
        'success',
      )
    },
  })
  const onConfirm = () => {
    if (selectedRecord) {
      const payload = {
        action: mode! as LeadDetailActionType,
        emailAddress: '',
        leadId: selectedRecord.leadId || 0,
        name: selectedRecord.name || '',
        priority: selectedRecord.priority,
        status: selectedRecord.status,
        type: selectedRecord.type,
      }
      mutateEditLeadDetail.mutateAsync(payload)
    }
  }
  const onCancel = () => {
    seCustomerRelationsDetail((prev) => ({
      ...prev,
      mode: undefined,
      selectedRecord: undefined,
    }))
  }

  return (
    <AppModalConfirmation
      confirmBtnText="Delete Lead"
      isLoading={mutateEditLeadDetail.isPending}
      isOpened
      modalTitle="Delete Lead"
      modalWidth={500}
      onCancel={onCancel}
      onConfirm={onConfirm}
      requiresPassword={false}
    >
      <div>
        <p className="mb-1 text-xs font-light leading-4">
          You are about to delete wealth lead -{' '}
          <span className="font-semibold">
            {selectedRecord?.name} {selectedRecord?.leadId}
          </span>
          .
        </p>
        <p className="text-xs font-light leading-4">
          This action is irreversible. Would you like to proceed?
        </p>
      </div>
    </AppModalConfirmation>
  )
}
