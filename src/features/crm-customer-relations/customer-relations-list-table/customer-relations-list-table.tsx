import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable, AppTag } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  CommonServices,
  CRMLeadServices,
  CUSTOMER_RELATIONS_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetLeadTableListResponse,
  type LeadStatus,
  type LeadType,
  type LeadPriority,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
  arrowHorizontalRightIcon,
} from '@/features/shared/ui/app-icons'
import {
  LeadPriorityLevels,
  LeadTypes,
  LeadStatuses,
} from '@/features/shared/mappings'

const VIEW_CUSTOMER_360_PERMISSION = 1600050
const EDIT_PERMISSION = 1500200
const DELETE_PERMISSION = 1500300

interface CustomerRelationsListTableProps {
  onRowClick: (
    record: GetLeadTableListResponse,
    action: 'JUMP' | 'EDIT' | 'DELETE',
  ) => void
}

export function CustomerRelationsListTable(
  props: CustomerRelationsListTableProps,
) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()

  const [searchParams] = useSearchParams()
  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const statusQuery = searchParams.get(SEARCH_PARAMS.status)
  const priorityQuery = searchParams.get(SEARCH_PARAMS.priority)
  const isMyLeadQuery = searchParams.get(SEARCH_PARAMS.isMyLead)
  const typeQuery = searchParams.get(SEARCH_PARAMS.type)
  const countryRegionQuery = searchParams.get(SEARCH_PARAMS.countryRegion)
  const createdDateFromQuery = searchParams.get(SEARCH_PARAMS.createdDateFrom)
  const createdDateToQuery = searchParams.get(SEARCH_PARAMS.createdDateTo)
  const assignedDateFromQuery = searchParams.get(SEARCH_PARAMS.assignedDateFrom)
  const assignedDateToQuery = searchParams.get(SEARCH_PARAMS.assignedDateTo)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      CRMLeadServices.getLeadList({
        assignedDateFrom: assignedDateFromQuery
          ? dayjs(Number(assignedDateFromQuery)).valueOf()
          : 0,
        assignedDateTo: assignedDateToQuery
          ? dayjs(Number(assignedDateToQuery)).valueOf()
          : 0,
        birthdayFrom: 0,
        birthdayTo: 0,
        countryRegion: countryRegionQuery || '',
        createdDateFrom: createdDateFromQuery
          ? dayjs(Number(createdDateFromQuery)).valueOf()
          : 0,
        createdDateTo: createdDateToQuery
          ? dayjs(Number(createdDateToQuery)).valueOf()
          : 0,
        isMyLead: isMyLeadQuery === 'My Lead' ? 'TRUE' : 'FALSE',
        keyword: keywordQuery || '',
        pageNum: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        priority: priorityQuery || '',
        searchType: keywordQuery ? searchTypeQuery || 'LEAD_ID' : '',
        sortBy: sortByQuery || 'CREATED_TIME',
        sortType: sortTypeQuery || 'DESC',
        status: statusQuery || '',
        type: typeQuery || '',
      }),
    queryKey: [
      'customer-relations-list',
      keywordQuery,
      searchTypeQuery,
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
      statusQuery,
      priorityQuery,
      isMyLeadQuery,
      typeQuery,
      countryRegionQuery,
      createdDateFromQuery,
      createdDateToQuery,
      assignedDateFromQuery,
      assignedDateToQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const { data: enumeration } = useQuery({
    queryFn: () =>
      CommonServices.getEnumeration({
        codeList: ['REGION'],
      }),
    queryKey: ['region'],
  })
  const countryListOptions = enumeration?.list
    .find((x) => x.code === 'REGION')
    ?.list.map((x) => ({
      label: x.desc,
      value: x.value,
    }))

  const columns: TableProps<GetLeadTableListResponse>['columns'] = [
    {
      dataIndex: 'leadId',
      key: 'leadId',
      title: 'Lead ID',
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: 'Lead Name',
    },
    {
      dataIndex: 'countryRegion',
      key: 'countryRegion',
      render: (countryRegion: string) =>
        countryRegion ? (
          <span>
            {countryListOptions?.find((o) => o.value === countryRegion)
              ?.label ?? countryRegion}
          </span>
        ) : (
          '-'
        ),
      title: 'Country/region',
    },
    {
      dataIndex: 'type',
      key: 'type',
      render: (type: string) =>
        type ? (
          <span
            className="font-light"
            style={{
              color: LeadTypes[type as LeadType].color ?? '#000',
            }}
          >
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              LeadTypes[type as LeadType]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      title: 'Types',
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (status: string) =>
        status ? (
          <span
            className="font-light"
            style={{
              color: LeadStatuses[status as LeadStatus].color ?? '#000',
            }}
          >
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              LeadStatuses[status as LeadStatus]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      title: 'Status',
    },
    {
      dataIndex: 'priority',
      key: 'PRIORITY',
      render: (priority: string) =>
        priority ? (
          <span
            className="font-light"
            style={{
              color:
                LeadPriorityLevels[priority as LeadPriority].color ?? '#000',
            }}
          >
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              LeadPriorityLevels[priority as LeadPriority]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      sortOrder:
        sortByQuery === 'PRIORITY'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Priority',
    },
    {
      dataIndex: 'tags',
      render: (tagList: GetLeadTableListResponse['tags']) =>
        tagList?.length ? (
          <div className="flex items-center gap-0.5">
            {tagList.slice(0, 3).map((tag, index) => (
              <AppTag key={index} variant="default">
                {tag}
              </AppTag>
            ))}
            {tagList.length > 3 && <span className="leading-[10px]">...</span>}
          </div>
        ) : (
          '--'
        ),
      title: 'Tags',
    },
    {
      dataIndex: 'assignedRm',
      key: 'assignedRm',
      title: 'Assigned RM',
    },
    {
      dataIndex: 'assignedTime',
      key: 'ASSIGNED_TIME',
      render: (assignedTime: string) => (
        <span>
          {assignedTime ? dayjs(assignedTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'ASSIGNED_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Assigned Date',
    },
    {
      dataIndex: 'createdTime',
      key: 'CREATED_TIME',
      render: (createTime: string) => (
        <span>
          {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'CREATED_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Created Date',
    },
    {
      dataIndex: 'leadId',
      key: 'leadId',
      render: (leadId: number, record) => (
        <Popover
          arrow={false}
          content={
            <div className="flex flex-col">
              {hasPermission(VIEW_CUSTOMER_360_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Go To Customer 360"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'JUMP')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={arrowHorizontalRightIcon}
                  type="button"
                />
              )}

              {hasPermission(EDIT_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Edit/View Lead"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'EDIT')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={editIcon}
                  type="button"
                />
              )}

              {hasPermission(DELETE_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Delete Lead"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'DELETE')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={trashIcon}
                  type="button"
                />
              )}
            </div>
          }
          onOpenChange={(newOpen) => {
            handleOpenChange(newOpen, leadId)
          }}
          open={selectedPopover === leadId}
          placement="bottomRight"
          trigger="click"
        >
          <AppButton
            cosmeticType="transparent"
            onClick={(e) => {
              e.stopPropagation()
            }}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={dotsThreeVerticalIcon}
              />
            }
            type="button"
          />
        </Popover>
      ),
      title: ' ',
      width: 40,
    },
  ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="leadId"
      total={data?.total}
    />
  )
}
