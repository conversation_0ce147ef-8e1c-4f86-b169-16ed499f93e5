import { useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useSetRecoilState } from 'recoil'
import dayjs from 'dayjs'
import { customerRelationsDetailState } from '@/features/shared/states'
import {
  AppButton,
  AppExportListModal,
  ContentHeader,
} from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'
import { CUSTOMER_RELATIONS_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'

const ADD_PERMISSION = 1500100
const EXPORT_PERMISSION = 1500050

export function CustomerRelationsContentHeader() {
  const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const [searchParams] = useSearchParams()
  const [isExportModalOpened, setIsExportModalOpened] = useState(false)

  const seCustomerRelationsDetail = useSetRecoilState(
    customerRelationsDetailState,
  )

  const onBack = () => {
    navigate('/admin-panel/crm/customer-relations/list')
  }

  const openCustomerRelationsModal = () => {
    seCustomerRelationsDetail({
      mode: 'ADD',
    })
  }

  const onExport = () => {
    setIsExportModalOpened(true)
  }

  const getExportFilters = () => {
    const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
    const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
    const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
    const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
    const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
    const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
    const statusQuery = searchParams.get(SEARCH_PARAMS.status)
    const priorityQuery = searchParams.get(SEARCH_PARAMS.priority)
    const isMyLeadQuery = searchParams.get(SEARCH_PARAMS.isMyLead)
    const countryRegionQuery = searchParams.get(SEARCH_PARAMS.countryRegion)
    const createdDateFromQuery = searchParams.get(SEARCH_PARAMS.createdDateFrom)
    const createdDateToQuery = searchParams.get(SEARCH_PARAMS.createdDateTo)
    const assignedDateFromQuery = searchParams.get(
      SEARCH_PARAMS.assignedDateFrom,
    )
    const assignedDateToQuery = searchParams.get(SEARCH_PARAMS.assignedDateTo)
    return {
      assignedDateFrom: assignedDateFromQuery
        ? dayjs(Number(assignedDateFromQuery)).valueOf()
        : 0,
      assignedDateTo: assignedDateToQuery
        ? dayjs(Number(assignedDateToQuery)).valueOf()
        : 0,
      birthdayFrom: 0,
      birthdayTo: 0,
      countryRegion: countryRegionQuery || '',
      createdDateFrom: createdDateFromQuery
        ? dayjs(Number(createdDateFromQuery)).valueOf()
        : 0,
      createdDateTo: createdDateToQuery
        ? dayjs(Number(createdDateToQuery)).valueOf()
        : 0,
      isMyLead: isMyLeadQuery === 'My Lead' ? 'TRUE' : 'FALSE',
      keyword: keywordQuery || '',
      pageNum: Number(pageNumQuery) || 1,
      pageSize: Number(pageSizeQuery) || 10,
      priority: priorityQuery || '',
      searchType: keywordQuery ? searchTypeQuery || 'LEAD_ID' : '',
      sortBy: sortByQuery || '',
      sortType: sortTypeQuery || '',
      status: statusQuery || '',
    }
  }

  return (
    <>
      <ContentHeader
        onBack={onBack}
        onExport={hasPermission(EXPORT_PERMISSION) ? onExport : undefined}
        title="Customer Relations"
      >
        {hasPermission(ADD_PERMISSION) && (
          <AppButton
            className="mr-2"
            label="Add"
            onClick={openCustomerRelationsModal}
            prefixIcon={plusGreenIcon}
          />
        )}
      </ContentHeader>
      {hasPermission(EXPORT_PERMISSION) && (
        <AppExportListModal
          exportFilters={getExportFilters()}
          isOpen={isExportModalOpened}
          onClose={() => {
            setIsExportModalOpened(false)
          }}
          type="customerRelations"
        />
      )}
    </>
  )
}
