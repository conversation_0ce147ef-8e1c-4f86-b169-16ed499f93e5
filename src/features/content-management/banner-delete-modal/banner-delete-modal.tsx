import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/features/shared/utils'
import { AppModalConfirmation } from '@/features/shared/ui'
import {
  type BannerDetailAction,
  type GetBannerTableListResponse,
  ContentBannerServices,
} from '@/features/shared/services'

interface BannerDeleteModalProps {
  mode: BannerDetailAction
  selectedRecord: GetBannerTableListResponse
  onClose: () => void
}

export function BannerDeleteModal(props: BannerDeleteModalProps) {
  const { mode, selectedRecord, onClose } = props

  const queryClient = useQueryClient()

  const { showToast } = useToast()

  const mutateDeleteBanner = useMutation({
    mutationFn: ContentBannerServices.deleteBanner,
    onError: (error) => {
      showToast('Banner Delete Failed', error.message, 'danger')
    },
    onSuccess: () => {
      onClose()
      queryClient.invalidateQueries({
        queryKey: ['banner-list'],
      })
      showToast(
        'Banner Delete',
        `You have deleted a banner - ${selectedRecord.title}`,
        'success',
      )
    },
  })
  const onConfirm = () => {
    const payload = {
      action: mode,
      id: selectedRecord.id,
    }
    mutateDeleteBanner.mutateAsync(payload)
  }

  return (
    <AppModalConfirmation
      confirmBtnText="Delete Banner"
      isLoading={mutateDeleteBanner.isPending}
      isOpened
      modalTitle="Delete Banner"
      modalWidth={500}
      onCancel={onClose}
      onConfirm={onConfirm}
      requiresPassword={false}
    >
      <div>
        <p className="mb-1 text-xs font-light leading-4">
          You are about to delete banner -{' '}
          <span className="font-semibold">
            {selectedRecord.title} {selectedRecord.id}
          </span>
          .
        </p>
        <p className="text-xs font-light leading-4">
          This action is irreversible. Would you like to proceed?
        </p>
      </div>
    </AppModalConfirmation>
  )
}
