import * as yup from 'yup'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect, useMemo, useState, type ChangeEvent } from 'react'
import dayjs, { type Dayjs } from 'dayjs'
import {
  AppButton,
  AppFileUploadInput,
  AppModal,
  AppNormalInput,
  ControlledAppChoiceInput,
  ControlledAppInputDatePicker,
  RequiredIndicator,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import {
  type BannerStatus,
  ContentBannerServices,
  type BannerDetailAction,
  CommonServices,
} from '@/features/shared/services'
import { BANNER_STATUS_OPTIONS } from '@/features/shared/mappings'
import { meritLogoGrayIcon } from '@/features/shared/ui/app-icons'

const EDIT_PERMISSION = 2100200

const QUERY_WAIT_TIME = 1500

const schema = yup
  .object()
  .shape({
    bannerId: yup.number().min(1, 'Account is a required field'),
    bannerName: yup.string().required('Banner Name is a required field'),
    endTime: yup.mixed<Dayjs>().notRequired(),
    imageFileKeyCn: yup
      .string()
      .required('Banner Image (Chinese version) is a required field'),
    imageFileKeyEn: yup
      .string()
      .required('Banner Image (English version) is a required field'),
    imageFileNameCn: yup.string(),
    imageFileNameEn: yup.string(),
    sortOrder: yup.string().required('Sort Order is a required field'),
    startTime: yup.mixed<Dayjs>().notRequired(),
    status: yup.string().required('Status is a required field'),
    targetUrl: yup.string(),
  })
  .required()

export type BannerAddEditFormValues = yup.InferType<typeof schema>

interface BannerAddEditModalProps {
  mode: BannerDetailAction
  initialValues: BannerAddEditFormValues
  existingBannerKeyCn?: string
  existingBannerKeyEn?: string
  onClose: () => void
}

export function BannerAddEditModal(props: BannerAddEditModalProps) {
  const {
    mode,
    initialValues,
    existingBannerKeyCn,
    existingBannerKeyEn,
    onClose,
  } = props

  const { hasPermission } = usePermission()
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const [uploadedFileEn, setUploadedFileEn] = useState<File | Blob>()
  const [uploadedFileCn, setUploadedFileCn] = useState<File | Blob>()
  const uploadedFileURLEn = useMemo(
    () => (uploadedFileEn ? URL.createObjectURL(uploadedFileEn) : ''),
    [uploadedFileEn],
  )
  const uploadedFileURLCn = useMemo(
    () => (uploadedFileCn ? URL.createObjectURL(uploadedFileCn) : ''),
    [uploadedFileCn],
  )

  const { data: existingFileCn, isLoading: isLoadingExistingFileCn } = useQuery(
    {
      enabled: Boolean(existingBannerKeyCn),
      queryFn: () => CommonServices.getFile(existingBannerKeyCn!),
      queryKey: ['getFile', existingBannerKeyCn],
    },
  )
  const { data: existingFileEn, isLoading: isLoadingExistingFileEn } = useQuery(
    {
      enabled: Boolean(existingBannerKeyEn),
      queryFn: () => CommonServices.getFile(existingBannerKeyEn!),
      queryKey: ['getFile', existingBannerKeyEn],
    },
  )

  const {
    control,
    handleSubmit,
    register,
    reset,
    setValue,
    formState: { isValid, errors },
    watch,
  } = useForm<BannerAddEditFormValues>({
    defaultValues: {
      ...initialValues,
    },
    resolver: yupResolver(schema),
  })
  const watchedBannerId = watch('bannerId')
  const watchedBannerName = watch('bannerName')
  const watchedImageFileNameCn = watch('imageFileNameCn')
  const watchedImageFileNameEn = watch('imageFileNameEn')

  const mutateAddBanner = useMutation({
    mutationFn: ContentBannerServices.addBanner,
    onError: (err) => {
      showToast('Banner Creation Failed', err.message, 'danger')
    },
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['banner-list'],
        })
      }, QUERY_WAIT_TIME)
      showToast(
        'Banner Created Successfully',
        `You have added a new banner`,
        'success',
      )
      __onClose()
    },
  })

  const mutateEditBanner = useMutation({
    mutationFn: ContentBannerServices.editBanner,
    onError: (err) => {
      showToast('Banner Update Failed', err.message, 'danger')
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['banner-list'],
      })
      showToast(
        'Banner Updated Successfully',
        `You have updated a banner - ${watchedBannerId} ${watchedBannerName}`,
        'success',
      )
      __onClose()
    },
  })

  const __onClose = () => {
    reset()
    resetUploadedFiles()
    onClose()
  }

  const __onSubmit = (data: BannerAddEditFormValues) => {
    if (mode === 'ADD') {
      const payload = {
        action: 'ADD' as BannerDetailAction,
        endTime: data.endTime ? data.endTime.endOf('day').valueOf() : undefined,
        imageFileKeyCn: data.imageFileKeyCn,
        imageFileKeyEn: data.imageFileKeyEn,
        sort: Number(data.sortOrder),
        startTime: data.startTime ? dayjs(data.startTime).valueOf() : undefined,
        status: data.status as BannerStatus,
        targetUrl: data.targetUrl ?? '',
        title: data.bannerName,
      }
      mutateAddBanner.mutateAsync(payload)
    } else if (data.bannerId) {
      const payload = {
        action: 'EDIT' as BannerDetailAction,
        endTime: data.endTime ? data.endTime.endOf('day').valueOf() : undefined,
        id: data.bannerId,
        imageFileKeyCn: data.imageFileKeyCn,
        imageFileKeyEn: data.imageFileKeyEn,
        sort: Number(data.sortOrder),
        startTime: data.startTime ? dayjs(data.startTime).valueOf() : undefined,
        status: data.status as BannerStatus,
        targetUrl: data.targetUrl ?? '',
        title: data.bannerName,
      }
      mutateEditBanner.mutateAsync(payload)
    }
  }

  const onChangeAllowNumbersOnly = (
    name: keyof BannerAddEditFormValues,
    event: ChangeEvent<HTMLInputElement>,
  ) => {
    let value = event.target.value
    value = value.replace(/[^\d.]+/g, '')
    setValue(name, value, {
      shouldDirty: true,
      shouldTouch: true,
    })
  }

  const getFileName = (filename: string) => {
    const nameChunks = filename.split('.')
    const fileName = nameChunks.slice(0, -1).join('.')
    // const fileType = nameChunks[nameChunks.length - 1]
    return fileName
  }

  const resetUploadedFiles = () => {
    setUploadedFileCn(undefined)
    setUploadedFileEn(undefined)
    URL.revokeObjectURL(uploadedFileURLCn)
    URL.revokeObjectURL(uploadedFileURLEn)
  }

  useEffect(() => {
    if (existingFileCn) {
      setUploadedFileCn(existingFileCn)
    }
  }, [existingFileCn])

  useEffect(() => {
    if (existingFileEn) {
      setUploadedFileEn(existingFileEn)
    }
  }, [existingFileEn])

  return (
    <AppModal
      childrenWrapperClassName="pb-0"
      onCancel={onClose}
      open
      titleLabel={mode === 'ADD' ? 'Add Banner' : 'Edit Banner'}
      width={600}
    >
      <form
        className="relative flex flex-col gap-3"
        onSubmit={handleSubmit(__onSubmit)}
      >
        <AppNormalInput
          {...register('bannerName')}
          error={errors.bannerName?.message}
          label="Name"
          placeholder="Input banner name"
          required
        />

        <section className="grid grid-cols-2 gap-2">
          <div className="space-y-3">
            <AppFileUploadInput
              accept=".jpg, .jpeg, .png"
              label="Upload Image (English)"
              maxFileSize={5}
              onChange={(fileKey, file) => {
                if (fileKey && file) {
                  setUploadedFileEn(file)
                  setValue('imageFileKeyEn', fileKey, {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                  setValue('imageFileNameEn', getFileName(file.name), {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                } else {
                  setUploadedFileEn(undefined)
                  setValue('imageFileKeyEn', '', {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                  setValue('imageFileNameEn', '', {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                }
              }}
              placeholder="Upload image"
              required
              value={watchedImageFileNameEn}
            />
            {uploadedFileURLEn && !isLoadingExistingFileEn ? (
              <img
                alt="thumbnail"
                className="aspect-[16/9] max-w-full object-cover"
                src={uploadedFileURLEn}
              />
            ) : (
              <div className="flex aspect-[16/9] w-full items-center justify-center bg-bg-tone">
                <img alt="thumbnail" src={meritLogoGrayIcon} />
              </div>
            )}
          </div>

          <div className="space-y-3">
            <AppFileUploadInput
              accept=".jpg, .jpeg, .png"
              label="Upload Image (Chinese)"
              maxFileSize={5}
              onChange={(fileKey, file) => {
                if (fileKey && file) {
                  setUploadedFileCn(file)
                  setValue('imageFileKeyCn', fileKey, {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                  setValue('imageFileNameCn', getFileName(file.name), {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                } else {
                  setUploadedFileCn(undefined)
                  setValue('imageFileKeyCn', '', {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                  setValue('imageFileNameCn', '', {
                    shouldDirty: true,
                    shouldValidate: true,
                  })
                }
              }}
              placeholder="Upload image"
              required
              value={watchedImageFileNameCn}
            />

            {uploadedFileURLCn && !isLoadingExistingFileCn ? (
              <img
                alt="thumbnail"
                className="aspect-[16/9] max-w-full object-cover"
                src={uploadedFileURLCn}
              />
            ) : (
              <div className="flex aspect-[16/9] w-full items-center justify-center bg-bg-tone">
                <img alt="thumbnail" src={meritLogoGrayIcon} />
              </div>
            )}
          </div>
        </section>

        <AppNormalInput
          {...register('targetUrl')}
          error={errors.targetUrl?.message}
          label="Target URL"
          placeholder="Input target URL"
        />

        <div className="grid grid-cols-2 gap-2">
          <AppNormalInput
            {...register('sortOrder', {
              onChange: (e: ChangeEvent<HTMLInputElement>) => {
                onChangeAllowNumbersOnly('sortOrder', e)
              },
            })}
            error={errors.sortOrder?.message}
            label="Sort Order"
            placeholder="Input sort order"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-2">
          <ControlledAppInputDatePicker
            control={control}
            label="Start Time"
            name="startTime"
          />
          <ControlledAppInputDatePicker
            control={control}
            label="End Time"
            name="endTime"
          />
        </div>

        <ControlledAppChoiceInput
          cols={3}
          control={control}
          label="Status"
          name="status"
          options={BANNER_STATUS_OPTIONS}
          required
        />

        <RequiredIndicator />

        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white pb-6 pt-3">
          <AppButton
            cosmeticType="skeleton"
            label="Cancel"
            onClick={__onClose}
          />
          {mode === 'ADD' && (
            <AppButton
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateAddBanner.isPending}
              label="Add Banner"
              type="submit"
            />
          )}
          {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
            <AppButton
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditBanner.isPending}
              label="Update Banner"
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
