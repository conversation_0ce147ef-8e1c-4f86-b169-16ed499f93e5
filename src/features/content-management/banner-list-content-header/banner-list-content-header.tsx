import { useNavigate } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { AppButton, ContentHeader } from '@/features/shared/ui'
import { plusIcon } from '@/features/shared/ui/app-icons'
import { usePermission } from '@/features/shared/utils'

const ADD_PERMISSION = 2100100

interface BannerListContentHeaderProps {
  onAdd: () => void
}

export function BannerListContentHeader(props: BannerListContentHeaderProps) {
  const { onAdd } = props
  const navigate = useNavigate()
  const { hasPermission } = usePermission()

  const handleBack = () => {
    navigate(-1)
  }

  return (
    <ContentHeader onBack={handleBack} title="Banner List">
      {hasPermission(ADD_PERMISSION) && (
        <AppButton
          label="Add Banner"
          onClick={onAdd}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={plusIcon}
            />
          }
        />
      )}
    </ContentHeader>
  )
}
