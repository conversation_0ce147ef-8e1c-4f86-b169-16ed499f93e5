import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  ContentBannerServices,
  BANNER_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetBannerTableListResponse,
  type BannerStatus,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
} from '@/features/shared/ui/app-icons'
import { BannerStatuses } from '@/features/shared/mappings'

const EDIT_PERMISSION = 2100200
const DELETE_PERMISSION = 2100300

interface BannerListTableProps {
  onRowClick: (
    record: GetBannerTableListResponse,
    action: 'EDIT' | 'DELETE',
  ) => void
}

export function BannerListTable(props: BannerListTableProps) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()

  const [searchParams] = useSearchParams()
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      ContentBannerServices.getBannerList({
        pageNum: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        sortBy: sortByQuery || 'CREATE_TIME',
        sortType: sortTypeQuery || 'DESC',
      }),
    queryKey: [
      'banner-list',
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const columns: TableProps<GetBannerTableListResponse>['columns'] = [
    {
      dataIndex: 'id',
      key: 'id',
      title: 'Banner ID',
    },
    {
      dataIndex: 'title',
      key: 'title',
      title: 'Name',
    },
    {
      dataIndex: 'sort',
      key: 'SORT',
      sortOrder:
        sortByQuery === 'SORT'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Sort Order',
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (status: string) =>
        status ? (
          <span
            className="font-light"
            style={{
              color: BannerStatuses[status as BannerStatus].color ?? '#000',
            }}
          >
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              BannerStatuses[status as BannerStatus]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      title: 'Status',
    },
    {
      dataIndex: 'startTime',
      key: 'START_TIME',
      render: (startTime: string) => (
        <span>
          {startTime ? dayjs(startTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'START_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Start Time',
    },
    {
      dataIndex: 'endTime',
      key: 'END_TIME',
      render: (endTime: string) => (
        <span>
          {endTime ? dayjs(endTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'END_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'End Time',
    },
    {
      dataIndex: 'createdAt',
      key: 'CREATE_TIME',
      render: (createTime: string) => (
        <span>
          {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'CREATE_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Created Time',
    },
    {
      dataIndex: 'id',
      key: 'id',
      render: (id: number, record) => (
        <Popover
          arrow={false}
          content={
            <div className="flex flex-col">
              {hasPermission(EDIT_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Edit/View Banner"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'EDIT')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={editIcon}
                  type="button"
                />
              )}

              {hasPermission(DELETE_PERMISSION) && (
                <AppButton
                  className="justify-start py-2 font-normal"
                  cosmeticType="transparent"
                  label="Delete Banner"
                  onClick={(e) => {
                    e.stopPropagation()
                    onRowClick(record, 'DELETE')
                    setSelectedPopover(null)
                  }}
                  prefixIcon={trashIcon}
                  type="button"
                />
              )}
            </div>
          }
          onOpenChange={(newOpen) => {
            handleOpenChange(newOpen, id)
          }}
          open={selectedPopover === id}
          placement="bottomRight"
          trigger="click"
        >
          <AppButton
            cosmeticType="transparent"
            onClick={(e) => {
              e.stopPropagation()
            }}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={dotsThreeVerticalIcon}
              />
            }
            type="button"
          />
        </Popover>
      ),
      title: ' ',
      width: 40,
    },
  ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="leadId"
      total={data?.total}
    />
  )
}
