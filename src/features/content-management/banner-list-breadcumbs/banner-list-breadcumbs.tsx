import { ReactSVG } from 'react-svg'
import { AppBreadcrumb } from '@/features/shared/ui'
import { listDashesIcon, listIcon } from '@/features/shared/ui/app-icons'

export function BannerListBreadcrumbs() {
  return (
    <div className="mb-4">
      <AppBreadcrumb
        breadcrumbs={[
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-label')
                }}
                src={listIcon}
              />
            ),
            label: 'Content Management',
            to: '/admin-panel/content-management/list',
          },
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={listDashesIcon}
              />
            ),
            label: 'Banner List',
          },
        ]}
      />
    </div>
  )
}
