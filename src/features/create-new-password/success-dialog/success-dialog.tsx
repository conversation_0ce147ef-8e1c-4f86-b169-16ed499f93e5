import { useNavigate } from 'react-router-dom'
import { checkCircleGreenIcon } from '@/features/shared/ui/app-icons'
import { AppButton } from '@/features/shared/ui'

export function SuccessDialog() {
  const navigate = useNavigate()

  return (
    <div className="flex flex-col items-center">
      <img
        alt="icon"
        className="mx-auto mt-8 h-[72px] w-[72px]"
        src={checkCircleGreenIcon}
      />
      <span className="mb-[68px] mt-1 block text-center text-base font-bold not-italic leading-6 text-txt-paragraph">
        All good!
      </span>
      <AppButton
        label="Login"
        onClick={() => {
          navigate('/login')
        }}
      />
    </div>
  )
}
