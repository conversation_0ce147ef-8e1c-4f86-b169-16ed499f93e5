import { ReactSVG } from 'react-svg'
import {
  checkCircleGreenIcon,
  uncheckedCircleIcon,
  xCircleIcon,
} from '@/features/shared/ui/app-icons'
import { cn } from '@/features/shared/utils'

interface ValidityStatusProps {
  isValid?: boolean
  description: string
}

export function ValidityStatus(props: ValidityStatusProps) {
  const { isValid, description } = props
  return (
    <div className="flex items-center gap-2">
      {isValid === undefined && (
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('w-4', 'fill-txt-inactive')
          }}
          src={uncheckedCircleIcon}
        />
      )}
      {isValid === true && (
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('w-4', 'fill-txt-success')
          }}
          src={checkCircleGreenIcon}
        />
      )}
      {isValid === false && (
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('w-4', 'fill-txt-negative')
          }}
          src={xCircleIcon}
        />
      )}
      <span
        className={cn(
          'text-xs font-light leading-[18px]',
          isValid ? 'text-txt-paragraph' : 'text-txt-inactive',
        )}
      >
        {description}
      </span>
    </div>
  )
}
