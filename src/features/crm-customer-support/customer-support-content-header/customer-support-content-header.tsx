import { ReactSVG } from 'react-svg'
import { AppButton, ContentHeader } from '@/features/shared/ui'
import { chatIcon, plusIcon } from '@/features/shared/ui/app-icons'
import { usePermission } from '@/features/shared/utils'

const START_NEW_CONVERSATION_PERMISSION = 2000100

interface CustomerSupportContentHeaderProps {
  onNewChat: () => void
}

export function CustomerSupportContentHeader(
  props: CustomerSupportContentHeaderProps,
) {
  const { onNewChat } = props
  const { hasPermission } = usePermission()

  return (
    <ContentHeader title="Chat List" titleIcon={chatIcon}>
      {hasPermission(START_NEW_CONVERSATION_PERMISSION) && (
        <AppButton
          className="min-w-[120px]"
          label="Start New Conversation"
          onClick={onNewChat}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={plusIcon}
            />
          }
        />
      )}
    </ContentHeader>
  )
}
