import { Ava<PERSON>, Badge } from 'antd'
import dayjs from 'dayjs'
import { AppButton } from '@/features/shared/ui'
import { userIcon } from '@/features/shared/ui/app-icons'
import { cn } from '@/features/shared/utils'
import { type ChatListConversation } from '@/features/shared/services'

interface ChatConversationProps {
  isOpened?: boolean
  data: ChatListConversation
  onClick: () => void
}

export function ChatConversation(props: ChatConversationProps) {
  const { data, isOpened, onClick } = props

  return (
    <AppButton
      className={cn(
        'grid min-h-[72px] grid-cols-[44px_1fr_8px] p-2 text-left',
        isOpened && 'bg-bg-tone',
      )}
      cosmeticType="transparent"
      onClick={onClick}
    >
      <div className="p-2">
        <Avatar src={userIcon} />
      </div>

      <section className="flex h-full flex-col gap-1">
        <div className="min-h-3 text-xs leading-3 text-txt-title">
          {data.latestMessageInfo.senderName}
        </div>
        <div>
          {data.latestMessageInfo.message ? (
            <p className="line-clamp-2 overflow-ellipsis text-xs font-light normal-case not-italic text-txt-paragraph">
              {data.latestMessageInfo.message}
            </p>
          ) : (
            <p className="font-light italic text-txt-inactive">
              (New Conversation)
            </p>
          )}
        </div>

        <span className="text-right text-[10px] font-light leading-3 text-txt-inactive">
          {dayjs(data.latestMessageSentTime).format('DD-MM-YYYY HH:mm')}
        </span>
      </section>

      {data.unreadMessageCount > 0 ? <Badge dot size="default" /> : null}
    </AppButton>
  )
}
