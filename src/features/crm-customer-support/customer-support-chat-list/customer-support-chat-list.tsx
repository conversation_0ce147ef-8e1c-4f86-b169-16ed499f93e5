import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { useRecoilState } from 'recoil'
import {
  type ChatListConversation,
  type ChatListSearchType,
  ChatServices,
} from '@/features/shared/services'
import { CHAT_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import { customerSupportState } from '@/features/shared/states'
import { NoData } from '@/features/shared/ui'
import { ChatConversation } from './ui'

const QUERY_REFETCH_INTERVAL = 5000

export function CustomerSupportChatList() {
  const [searchParams] = useSearchParams()
  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)

  const [{ activeChatId }, setCustomerSupportState] =
    useRecoilState(customerSupportState)

  const { data } = useQuery({
    placeholderData: keepPreviousData,
    queryFn: () =>
      ChatServices.getChatList({
        keyword: keywordQuery || '',
        searchType: (searchTypeQuery || 'CUSTOMER_NAME') as ChatListSearchType,
      }),
    queryKey: ['chat-list', keywordQuery, searchTypeQuery],
    refetchInterval: QUERY_REFETCH_INTERVAL,
  })
  const chatList = data?.chatList ?? []

  const onOpenChat = (convo: ChatListConversation) => {
    setCustomerSupportState((prev) => ({
      ...prev,
      activeChatCustomerId: convo.customerId,
      activeChatCustomerName: convo.latestMessageInfo.senderName,
      activeChatId: convo.chatId,
    }))
  }

  return (
    <>
      {chatList.length > 0 ? (
        /* NOTE: Show 6 conversations = (6 * 72px height) + (5 * 4px gap) */
        <div className="flex h-[452px] flex-col gap-1 overflow-y-auto">
          {chatList.map((convo) => (
            <ChatConversation
              data={convo}
              isOpened={activeChatId === convo.chatId}
              key={convo.chatId}
              onClick={() => {
                onOpenChat(convo)
              }}
            />
          ))}
        </div>
      ) : (
        <NoData />
      )}
    </>
  )
}
