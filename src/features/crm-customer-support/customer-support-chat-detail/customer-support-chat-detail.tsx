import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { ChatServices } from '@/features/shared/services'
import { AppChatEmptyPlaceholder, AppChatThread } from '@/features/shared/ui'
import { ChatDetailFooter, ChatDetailHeader } from './ui'

const CURRENT_TIME = dayjs().valueOf()
const QUERY_REFETCH_INTERVAL = 5000

interface CustomerSupportChatDetailProps {
  customerId: number
  customerName: string
}

export function CustomerSupportChatDetail(
  props: CustomerSupportChatDetailProps,
) {
  const { customerId, customerName } = props
  const [referenceTime, setReferenceTime] = useState(CURRENT_TIME)

  const { data, isLoading } = useQuery({
    enabled: Boolean(customerId),
    placeholderData: keepPreviousData,
    queryFn: () =>
      ChatServices.getChatMessages({
        customerId,
        direction: 'BEFORE',
        limit: 20,
        referenceTime,
      }),
    queryKey: ['chat-messages-by-id', customerId, referenceTime],
    refetchInterval: QUERY_REFETCH_INTERVAL,
  })
  const messageList = data?.messageList ?? []

  const onSendSuccess = () => {
    setReferenceTime(dayjs().valueOf())
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setReferenceTime(dayjs().valueOf())
    }, QUERY_REFETCH_INTERVAL)

    return () => {
      clearInterval(interval)
    } // Clear interval on component unmount
  }, [])

  return (
    <section className="flex h-full flex-col gap-2 rounded-sm bg-bg-default px-6 pt-6">
      <ChatDetailHeader customerId={customerId} customerName={customerName} />
      <div className="max-h-full grow overflow-y-auto">
        {messageList.length > 0 ? (
          <div className="flex flex-col gap-1">
            {messageList.map((message, index) => (
              <AppChatThread
                data={message}
                isFirstMessage={
                  index > 0
                    ? messageList[index - 1]?.senderId !== message.senderId
                    : true
                }
                key={index}
              />
            ))}
          </div>
        ) : null}
        {messageList.length === 0 && !isLoading && <AppChatEmptyPlaceholder />}
      </div>
      <ChatDetailFooter customerId={customerId} onSendSuccess={onSendSuccess} />
    </section>
  )
}
