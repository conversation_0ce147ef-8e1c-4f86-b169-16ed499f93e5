import { ReactSVG } from 'react-svg'
import { MeritDivider } from '@/features/shared/ui'
import { chatIcon } from '@/features/shared/ui/app-icons'

interface ChatDetailHeaderProps {
  customerName: string
  customerId: number
}

export function ChatDetailHeader(props: ChatDetailHeaderProps) {
  const { customerName, customerId } = props

  return (
    <div className="space-y-1">
      <section className="flex items-center gap-1">
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('fill-txt-title', 'w-3.5', 'h-3.5')
          }}
          src={chatIcon}
        />
        <h2 className="text-sm font-semibold leading-[14px]">
          {customerName} - {customerId}
        </h2>
      </section>
      <MeritDivider />
    </div>
  )
}
