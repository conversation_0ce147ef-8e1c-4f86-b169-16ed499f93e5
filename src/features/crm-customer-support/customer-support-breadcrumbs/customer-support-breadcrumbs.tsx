import { ReactSVG } from 'react-svg'
import { AppBreadcrumb } from '@/features/shared/ui'
import { chatIcon } from '@/features/shared/ui/app-icons'

export function CustomerSupportBreadcrumbs() {
  return (
    <div>
      <AppBreadcrumb
        breadcrumbs={[
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-paragraph', 'w-3.5', 'h-3.5')
                }}
                src={chatIcon}
              />
            ),
            label: 'Chat List',
          },
        ]}
      />
    </div>
  )
}
