import type { SelectProps } from 'antd'
import { AutoComplete, Input } from 'antd'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useQuery } from '@tanstack/react-query'
import {
  type FieldValues,
  type UseControllerProps,
  useController,
} from 'react-hook-form'
import {
  magnifyingGlassIcon,
  xCircleDarkIcon,
} from '@/features/shared/ui/app-icons'
import {
  CustomerServices,
  type SearchCustomerListResponse,
} from '@/features/shared/services'
import { SelectedCustomerItem } from './selected-customer-item'

interface CustomerSearchInputProps {
  onChange?: (symbol: string) => void
  onClear?: () => void
  onSelect: (option: SearchCustomerListResponse) => void
  disabled?: boolean
  label?: string
  refineStatus?: string
  required?: boolean
  value?: string
  placeholder?: string
}

export function CustomerSearchInput(props: CustomerSearchInputProps) {
  const {
    label,
    disabled,
    required,
    onSelect,
    refineStatus = '',
    value,
    onClear,
    onChange,
    placeholder = 'Search customer ID or code ...',
  } = props
  const [searchQuery, setSearchQuery] = useState(value)
  const [autoCompleteValue, setAutoCompleteValue] = useState(value)

  const { data: customerListData } = useQuery({
    enabled: searchQuery !== '',
    queryFn: () =>
      CustomerServices.searchCustomerList({
        approvalStatus: refineStatus,
        createFromTime: 0,
        createToTime: 0,
        isVip: undefined,
        keyword: String(searchQuery) || '',
        pageNum: 1,
        pageSize: 99999,
        riskLevel: '',
        searchType: 'CUSTOMER_NAME_OR_ID',
        sortBy: '',
        sortType: '',
        updateFromTime: 0,
        updateToTime: 0,
      }),
    queryKey: ['customer-search', searchQuery, refineStatus],
  })

  const customerList = customerListData?.list || []
  const options: SelectProps<object>['options'] = customerList.map(
    (customer) => ({
      item: customer,
      label: <SelectedCustomerItem data={customer} />,
      value: customer.customerId,
    }),
  )
  const selectedOption = options.find((option) => option.value === searchQuery)

  useEffect(() => {
    setSearchQuery(value)
  }, [value])

  return (
    <div>
      {label ? (
        <h4 className="mb-2 text-xs font-medium">
          {label}
          {required ? (
            <span className="ml-1 text-txt-negative">*</span>
          ) : null}{' '}
        </h4>
      ) : null}

      {value ? (
        <div className="flex items-center justify-between rounded-sm bg-bg-tone px-3">
          <div className="h-[44px] py-2.5">
            <span className="text-xs font-light leading-3">
              {selectedOption ? selectedOption.label : options[0]?.label}
            </span>
          </div>
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title', 'cursor-pointer')
            }}
            onClick={() => {
              onChange?.('')
              onClear?.()
              setSearchQuery('')
              setAutoCompleteValue('')
            }}
            src={xCircleDarkIcon}
          />
        </div>
      ) : (
        <AutoComplete
          disabled={disabled}
          onSearch={(value) => {
            setSearchQuery(value)
            setAutoCompleteValue(value)
          }}
          onSelect={(value, option) => {
            const selectedCustomer: SearchCustomerListResponse = option.item
            onChange?.(value)
            onSelect(selectedCustomer)
            setSearchQuery(String(selectedCustomer.customerId))
            setAutoCompleteValue('')
          }}
          options={options}
          style={{ height: 'auto', width: '100%' }}
          value={autoCompleteValue}
        >
          <Input
            className="whitespace-no-wrap h-11 w-full overflow-hidden rounded-sm border-none bg-bg-tone px-3 py-2.5 text-xs font-light leading-[14px] shadow-sm ring-inset placeholder:text-txt-inactive hover:bg-bg-tone focus:bg-bg-tone focus:ring-0"
            disabled={disabled}
            placeholder={placeholder}
            prefix={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={magnifyingGlassIcon}
              />
            }
          />
        </AutoComplete>
      )}
    </div>
  )
}

type ControlledCustomerSearchInputProps<T extends FieldValues> =
  UseControllerProps<T> & CustomerSearchInputProps
export function ControlledCustomerSearchInput<T extends FieldValues>(
  props: ControlledCustomerSearchInputProps<T>,
) {
  const {
    name,
    control,
    rules,
    onClear,
    onSelect,
    label,
    refineStatus,
    required,
    placeholder,
  } = props

  const {
    field: { ref: _, onChange, ...field },
  } = useController({ control, name, rules })
  return (
    <CustomerSearchInput
      label={label}
      onChange={onChange}
      onClear={onClear}
      onSelect={onSelect}
      placeholder={placeholder}
      refineStatus={refineStatus}
      required={required}
      {...field}
    />
  )
}
