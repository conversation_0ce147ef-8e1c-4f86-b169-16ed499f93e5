import Avatar from 'antd/es/avatar/avatar'
import { ReactSVG } from 'react-svg'
import { trashFilledIcon, userIcon } from '@/features/shared/ui/app-icons'
import { AppButton, AppTag } from '@/features/shared/ui'
import {
  type CustomerRiskLevelKeys,
  type SearchCustomerListResponse,
} from '@/features/shared/services'
import { CustomerRiskLevels } from '@/features/shared/mappings'

interface SelectedCustomerItemProps {
  data: SearchCustomerListResponse
  onDelete?: () => void
}

export function SelectedCustomerItem(props: SelectedCustomerItemProps) {
  const { data, onDelete } = props

  return (
    <div className="grid w-full grid-cols-[32px_1fr_auto_auto] items-center gap-2 p-2">
      <Avatar size="large" src={userIcon}>
        {data.customerName}
      </Avatar>
      <div className="flex flex-col gap-1">
        <span className="text-sm font-bold leading-[14px] text-txt-title">
          {data.customerName} / {data.customerNameCN}
        </span>
        <span className="text-xs font-medium leading-[14px] text-txt-paragraph">
          {data.customerId}
        </span>
      </div>
      <div>
        {data.riskLevel === CustomerRiskLevels.HIGH.key && (
          <AppTag variant="danger">{CustomerRiskLevels.HIGH.label}</AppTag>
        )}
        {data.riskLevel === CustomerRiskLevels.MEDIUM.key && (
          <AppTag variant="caution">{CustomerRiskLevels.MEDIUM.label}</AppTag>
        )}
        {data.riskLevel !== CustomerRiskLevels.HIGH.key &&
          data.riskLevel !== CustomerRiskLevels.MEDIUM.key && (
            <AppTag variant="primary">
              {
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
                CustomerRiskLevels[data.riskLevel as CustomerRiskLevelKeys]
                  ? CustomerRiskLevels[data.riskLevel as CustomerRiskLevelKeys]
                      .label
                  : data.riskLevel
              }
            </AppTag>
          )}
      </div>

      {onDelete ? (
        <AppButton
          className="p-0"
          cosmeticType="transparent"
          onClick={onDelete}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('w-4', 'h-4', 'fill-txt-title')
              }}
              src={trashFilledIcon}
            />
          }
        />
      ) : null}
    </div>
  )
}
