import * as yup from 'yup'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AppButton, AppModal } from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import {
  ChatServices,
  type SearchCustomerListResponse,
} from '@/features/shared/services'
import { CustomerSearchInput } from './ui'
import { SelectedCustomerItem } from './ui/selected-customer-item'

const schema = yup
  .object()
  .shape({
    customerId: yup
      .number()
      .min(1, 'Please select a customer')
      .required('Please select a customer'),
  })
  .required()
type FormData = yup.InferType<typeof schema>

interface CustomerSupportAddChatModalProps {
  onCancel: () => void
}

export function CustomerSupportAddChatModal(
  props: CustomerSupportAddChatModalProps,
) {
  const { onCancel } = props
  const { showToast } = useToast()
  const queryClient = useQueryClient()
  const [selectedCustomer, setSelectedCustomer] =
    useState<SearchCustomerListResponse>()

  const {
    handleSubmit,
    formState: { isValid },
    setValue,
  } = useForm<FormData>({
    mode: 'onChange',
    resolver: yupResolver(schema),
  })

  const mutateAddConversation = useMutation({
    mutationFn: ChatServices.addNewConversation,
    onError: () => {
      showToast(
        `Failed to Start Conversation`,
        `No matching customer found. Please try again.`,
        'danger',
      )
    },
    onSuccess: () => {
      showToast(
        `New Conversation Added`,
        `You have started a new conversation with the customer - ${selectedCustomer?.customerName} ${selectedCustomer?.customerId}`,
        'success',
      )
      queryClient.invalidateQueries({
        queryKey: ['chat-list'],
      })
      onCancel()
    },
  })

  const onRemoveCustomer = () => {
    setValue('customerId', 0, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    })
    setSelectedCustomer(undefined)
  }

  const onSelectCustomer = (customer: SearchCustomerListResponse) => {
    setValue('customerId', customer.customerId, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    })
    setSelectedCustomer(customer)
  }

  const onSubmit = (data: FormData) => {
    const payload = {
      customerId: data.customerId,
    }
    mutateAddConversation.mutateAsync(payload)
  }

  return (
    <AppModal
      onCancel={onCancel}
      open
      titleLabel="Start New Conversation"
      width={600}
    >
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(onSubmit)}>
        <section className="min-h-[240px]">
          {selectedCustomer ? (
            <SelectedCustomerItem
              data={selectedCustomer}
              onDelete={onRemoveCustomer}
            />
          ) : (
            <CustomerSearchInput
              label="Subject Client"
              onSelect={onSelectCustomer}
              placeholder="Search Client ..."
              refineStatus="APPROVED"
              required
            />
          )}
        </section>

        <section className="grid grid-cols-2 gap-2">
          <AppButton
            cosmeticType="skeleton"
            label="Cancel"
            onClick={onCancel}
            type="button"
          />
          <AppButton
            cosmeticType="secondary"
            disabled={!isValid}
            label="Start Conversation"
            type="submit"
          />
        </section>
      </form>
    </AppModal>
  )
}
