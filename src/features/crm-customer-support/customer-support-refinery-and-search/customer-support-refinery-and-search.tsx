import Select, { type BaseOptionType } from 'antd/es/select'
import { useSearchParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { useEffect, useState } from 'react'
import { useDebounceValue } from 'usehooks-ts'
import {
  caretDownIcon,
  magnifyingGlassIcon,
} from '@/features/shared/ui/app-icons'
import { AppInputBeta } from '@/features/shared/ui/app-input-beta'

const FILTER_OPTIONS: BaseOptionType[] = [
  {
    label: 'Customer Name',
    value: 'CUSTOMER_NAME',
  },
  {
    label: 'Customer ID',
    value: 'CUSTOMER_ID',
  },
]

export function CustomerSupportRefineryAndSearch() {
  const [searchParams, setSearchParams] = useSearchParams()
  const keywordQuery = searchParams.get('keyword')
  const [keyword, setKeyword] = useState(keywordQuery || '')
  const [debouncedKeyword, setDebouncedKeyword] = useDebounceValue(
    keyword || '',
    300,
  )
  const searchTypeQuery = searchParams.get('searchType')
  const [searchType, setSearchType] = useState(searchTypeQuery)

  useEffect(() => {
    setDebouncedKeyword(keyword)
  }, [keyword, setDebouncedKeyword])

  useEffect(() => {
    setSearchParams((prev) => {
      if (debouncedKeyword) {
        prev.set('keyword', debouncedKeyword)
      } else {
        prev.delete('keyword')
      }
      return prev
    })
  }, [debouncedKeyword, setSearchParams])

  useEffect(() => {
    setSearchParams((prev) => {
      if (searchType) prev.set('searchType', searchType)
      else prev.delete('searchType')
      return prev
    })
  }, [searchType, setSearchParams])

  return (
    <section className="flex h-[42px] justify-between">
      <div className="flex w-full items-center bg-bg-accent py-1">
        <Select
          className="list-search-select"
          onChange={(value) => {
            setKeyword('')
            setTimeout(() => {
              setSearchType(value)
            }, 500)
          }}
          options={FILTER_OPTIONS}
          style={{ width: 150 }}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={caretDownIcon}
            />
          }
          value={searchType || 'CUSTOMER_NAME'}
        />
        <div className="h-[22px] w-[1px] rounded bg-txt-inactive" />
        <AppInputBeta
          onChange={(e) => {
            setKeyword(e.target.value)
          }}
          placeholder="Search"
          prefixElement={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={magnifyingGlassIcon}
            />
          }
          value={keyword}
          wrapperClassName="w-full"
        />
      </div>
    </section>
  )
}
