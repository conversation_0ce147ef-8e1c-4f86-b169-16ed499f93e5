import { useRecoilState } from 'recoil'
import { useQuery } from '@tanstack/react-query'
import { customerAccountListState } from '@/features/shared/states/customer'
import { AppModal } from '@/features/shared/ui'
import { CustomerAccountServices } from '@/features/shared/services'
import { AccountDetailTabs } from './account-detail-tabs'
import { AccountDetailInformation } from './account-detail-information'
import { AccountDetailSubAccount } from './account-detail-sub-account'

export function AccountDetailModal() {
  const [{ accountId, addEditMode, viewActiveTab }, setCustomerAccountList] =
    useRecoilState(customerAccountListState)

  const { data: accountDetail } = useQuery({
    enabled: Boolean(accountId),
    queryFn: () =>
      CustomerAccountServices.getAccountDetail({
        accountId: accountId!,
      }),
    queryKey: ['customer-account-detail', accountId],
  })

  const onModalClose = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        accountId: undefined,
      }
    })
  }

  return (
    <AppModal
      onCancel={onModalClose}
      open={Boolean(accountId && !addEditMode)}
      titleDescription={`- ${accountId}`}
      titleLabel="View Account"
      width={720}
      childrenWrapperClassName={
        viewActiveTab === 'sub-accounts'
          ? 'pb-0 min-h-[380px]'
          : 'min-h-[380px]'
      } // pb-0: required for AccountDetailSubAccount
    >
      <AccountDetailTabs />
      {viewActiveTab === 'information' ? (
        <AccountDetailInformation
          accountDetail={accountDetail?.AccountDetail}
          activeAccounts={accountDetail?.activeAccounts ?? 0}
          inactiveAccounts={accountDetail?.inactiveAccounts ?? 0}
        />
      ) : (
        <AccountDetailSubAccount
          subAccountList={accountDetail?.subAccountList ?? []}
        />
      )}
    </AppModal>
  )
}
