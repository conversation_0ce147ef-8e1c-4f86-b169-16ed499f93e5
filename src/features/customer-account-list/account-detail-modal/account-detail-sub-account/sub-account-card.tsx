import type { ChangeEvent } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  type AccountType,
  CustomerAccountServices,
  type CustomerAccount,
} from '@/features/shared/services'
import {
  AppEditButton,
  AppInfoCardVariant9,
  AppToggleInput,
} from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import { AccountStatuses, AccountTypes } from '@/features/shared/mappings'

interface SubAccountCardProps {
  subAccount: CustomerAccount
  canEdit?: boolean
  onEdit: (subAccountId: number) => void
}

export function SubAccountCard(props: SubAccountCardProps) {
  const { subAccount, canEdit, onEdit } = props
  const { showToast } = useToast()
  const queryClient = useQueryClient()

  const mutateSubAccountEdit = useMutation({
    mutationFn: CustomerAccountServices.editAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['customer-account-detail'],
      })
      showToast(
        'Account Updated',
        `You have updated a sub account - ${subAccount.accountId} - under account - ${subAccount.parentAccountID}`,
        'success',
      )
    },
  })

  const onToggleAccountStatus = (accountId: number, value: boolean) => {
    const payload = {
      accountCode: subAccount.accountCode,
      accountId,
      accountName: subAccount.accountName,
      accountStatus: value
        ? AccountStatuses.NORMAL.key
        : AccountStatuses.INACTIVE.key,
      accountType: subAccount.accountType,
      brokerCode: subAccount.brokerCode,
      externalAccountCode: subAccount.externalAccountCode,
    }
    mutateSubAccountEdit.mutateAsync(payload)
  }

  return (
    <section className="flex flex-col gap-1">
      <div className="flex items-center justify-between gap-1">
        <h4 className="block text-xs font-bold not-italic leading-3">
          Sub Account {subAccount.accountId}
        </h4>
        <AppToggleInput
          checked={subAccount.accountStatus === 'NORMAL'}
          disabled={!canEdit}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            onToggleAccountStatus(subAccount.accountId, e.target.value === 'on')
          }}
          variant="positive"
        />
      </div>

      <div className="grid gap-x-5 sm:grid-cols-2">
        <AppInfoCardVariant9
          contents={[
            {
              label: 'Account ID',
              value: subAccount.accountId,
            },
            {
              label: 'Account Code',
              value: subAccount.accountCode,
            },
            {
              label: 'Account Name',
              value: subAccount.accountName,
            },
            {
              label: 'Account Type',
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              value: AccountTypes[subAccount.accountType as AccountType]
                ? AccountTypes[subAccount.accountType as AccountType].label
                : subAccount.accountType,
            },
          ]}
        />
        <AppInfoCardVariant9
          contents={[
            {
              label: 'Broker Code',
              value: subAccount.brokerCode,
            },
            {
              label: 'Broker Name',
              value: subAccount.brokerName,
            },
            {
              label: 'External Account Code',
              value: subAccount.brokerCode,
            },
          ]}
        />
      </div>

      {canEdit ? (
        <AppEditButton
          isColorful
          onClick={() => {
            onEdit(subAccount.accountId)
          }}
          wrapperClassName="mx-auto"
        />
      ) : null}
    </section>
  )
}
