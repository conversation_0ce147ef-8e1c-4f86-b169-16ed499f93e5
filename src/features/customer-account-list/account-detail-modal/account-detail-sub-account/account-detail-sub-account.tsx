import { useSetRecoilState } from 'recoil'
import { ReactSVG } from 'react-svg'
import { type CustomerAccount } from '@/features/shared/services'
import { AppButton, MeritDivider } from '@/features/shared/ui'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'
import { customerAccountListState } from '@/features/shared/states/customer'
import { usePermission } from '@/features/shared/utils'
import { SubAccountCard } from './sub-account-card'

const ADD_PERMISSION = 400100
const EDIT_PERMISSION = 400200

interface AccountDetailSubAccountProps {
  subAccountList: CustomerAccount[]
}

export function AccountDetailSubAccount(props: AccountDetailSubAccountProps) {
  const { subAccountList } = props
  const { hasPermission } = usePermission()
  const setCustomerAccountList = useSetRecoilState(customerAccountListState)

  const onAdd = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        addEditAccountType: 'SUB_ACCOUNT',
        addEditMode: 'ADD',
      }
    })
  }

  const onEdit = (subAccountId: number) => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        addEditAccountType: 'SUB_ACCOUNT',
        addEditMode: 'EDIT',
        subAccount: subAccountList.find(
          (item) => item.accountId === subAccountId,
        ),
      }
    })
  }

  return (
    <>
      <div className="flex flex-col gap-4">
        {subAccountList.length === 0 ? (
          <div className="flex h-[100px] items-end justify-center text-xs leading-3 text-txt-inactive">
            No Sub Accounts
          </div>
        ) : (
          subAccountList.map((subAccount) => (
            <>
              <SubAccountCard
                canEdit={hasPermission(EDIT_PERMISSION)}
                key={subAccount.accountId}
                onEdit={onEdit}
                subAccount={subAccount}
              />
              <MeritDivider />
            </>
          ))
        )}
      </div>
      {hasPermission(ADD_PERMISSION) && (
        <section className="sticky bottom-0 left-0 right-0 bg-bg-default p-6">
          <AppButton
            className="mx-auto w-[200px]"
            cosmeticType="quaternary"
            label="Add Sub Account"
            onClick={onAdd}
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={plusGreenIcon}
              />
            }
          />
        </section>
      )}
    </>
  )
}
