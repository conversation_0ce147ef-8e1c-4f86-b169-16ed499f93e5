import { useRecoilState } from 'recoil'
import dayjs from 'dayjs'
import { customerAccountListState } from '@/features/shared/states/customer'
import {
  AppEditButton,
  AppInfoCardVariant9,
  MeritDivider,
} from '@/features/shared/ui'
import {
  type AccountType,
  type CustomerAccount,
} from '@/features/shared/services'
import { AccountTypes } from '@/features/shared/mappings/customer-account'
import { usePermission } from '@/features/shared/utils'

const EDIT_PERMISSION = 400200

interface AccountDetailInformationProps {
  activeAccounts: number
  inactiveAccounts: number
  accountDetail?: CustomerAccount
}

export function AccountDetailInformation(props: AccountDetailInformationProps) {
  const { activeAccounts, inactiveAccounts, accountDetail } = props
  const { hasPermission } = usePermission()
  const [{ accountId }, setCustomerAccountList] = useRecoilState(
    customerAccountListState,
  )

  const onEdit = (accountId: number) => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        accountId,
        addEditAccountType: 'ACCOUNT',
        addEditMode: 'EDIT',
      }
    })
  }

  return (
    <div className="flex flex-col gap-3">
      <section>
        <AppInfoCardVariant9
          cols={2}
          contents={[
            {
              label: 'Active Accounts',
              value: String(activeAccounts),
              valueColor: activeAccounts
                ? 'rgb(var(--color-txt-positive))'
                : '',
            },
            {
              label: 'Inactive Accounts',
              value: String(inactiveAccounts),
              valueColor: inactiveAccounts
                ? 'rgb(var(--color-txt-caution))'
                : '',
            },
          ]}
        />
      </section>

      <MeritDivider />

      <section>
        <h4 className="mb-2 text-xs font-medium leading-4 text-txt-title">
          Basic Information
        </h4>
        <div className="grid gap-x-5 sm:grid-cols-2">
          <AppInfoCardVariant9
            contents={[
              {
                label: 'Account ID',
                value: accountDetail?.accountId,
              },
              {
                label: 'Account Code',
                value: accountDetail?.accountCode,
              },
              {
                label: 'Account Name',
                value: accountDetail?.accountName,
              },
              {
                label: 'Account Type',
                value: accountDetail?.accountType
                  ? // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
                    AccountTypes[accountDetail.accountType as AccountType]
                      .label ?? accountDetail.accountType
                  : '',
              },
            ]}
          />
          <AppInfoCardVariant9
            contents={[
              {
                label: 'Broker Code',
                value: accountDetail?.brokerCode,
              },
              {
                label: 'Broker Name',
                value: accountDetail?.brokerName,
              },
              {
                label: 'External Account Code',
                value: accountDetail?.brokerCode,
              },
            ]}
          />
        </div>
        {hasPermission(EDIT_PERMISSION) && accountId ? (
          <AppEditButton
            isColorful
            onClick={() => {
              onEdit(accountId)
            }}
            wrapperClassName="mx-auto mt-2"
          />
        ) : null}
      </section>

      <MeritDivider />

      <AppInfoCardVariant9
        cols={2}
        contents={[
          {
            label: 'Created On',
            value: accountDetail?.createdAt ? (
              <span>
                {dayjs(accountDetail.createdAt).format('DD-MM-YY HH:MM')}
              </span>
            ) : undefined,
          },
          {
            label: 'Updated On',
            value: accountDetail?.updatedOn ? (
              <span>
                {dayjs(accountDetail.updatedOn).format('DD-MM-YY HH:MM')}
              </span>
            ) : undefined,
          },
        ]}
      />
    </div>
  )
}
