import { useRecoilState } from 'recoil'
import {
  customerAccountListState,
  type CustomerAccountViewTabs,
} from '@/features/shared/states/customer'
import { AppSwitchButton } from '@/features/shared/ui'

const tabs: {
  key: CustomerAccountViewTabs
  label: string
}[] = [
  {
    key: 'information',
    label: 'Information',
  },
  {
    key: 'sub-accounts',
    label: 'Sub-Accounts',
  },
]

export function AccountDetailTabs() {
  const [{ viewActiveTab }, setCustomerAccountList] = useRecoilState(
    customerAccountListState,
  )

  const onViewTabChange = (tab: CustomerAccountViewTabs) => {
    setCustomerAccountList((prev) => {
      return { ...prev, viewActiveTab: tab }
    })
  }

  return (
    <div className="mb-3 grid w-full grid-cols-2">
      {tabs.map((tab) => (
        <AppSwitchButton
          isActive={tab.key === viewActiveTab}
          key={tab.key}
          onClick={() => {
            onViewTabChange(tab.key)
          }}
        >
          {tab.label}
        </AppSwitchButton>
      ))}
    </div>
  )
}
