import { useRecoilState } from 'recoil'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useForm } from 'react-hook-form'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { type SelectProps } from 'antd'
import { customerAccountListState } from '@/features/shared/states/customer'
import {
  AppButton,
  AppInfoCardVariant9,
  AppModal,
  AppNormalInput,
  ControlledAppChoiceInput,
  ControlledAppSelectInput,
  MeritDivider,
  RequiredIndicator,
} from '@/features/shared/ui'
import {
  type AccountStatus,
  type AccountType,
  CommonServices,
  CustomerAccountServices,
} from '@/features/shared/services'
import {
  AccountStatusOptions,
  AccountTypeOptions,
} from '@/features/shared/mappings/customer-account'
import { useToast } from '@/features/shared/utils'

const schema = yup
  .object()
  .shape({
    accountCode: yup.string().required(),
    accountName: yup.string().required('Account Name is a required field'),
    accountStatus: yup.string().required(),
    accountType: yup.string().required(),
    brokerCode: yup.string().required(),
    externalAccountCode: yup.string().required(),
  })
  .required()

type FormData = yup.InferType<typeof schema>

export function AccountAddEditModal() {
  const { showToast } = useToast()
  const queryClient = useQueryClient()
  const [
    { accountId, addEditAccountType, addEditMode, customerId, customerName },
    setCustomerAccountList,
  ] = useRecoilState(customerAccountListState)

  const {
    control,
    formState: { isValid, errors },
    handleSubmit,
    register,
    reset,
  } = useForm<FormData>({
    defaultValues: {
      accountCode: '',
      accountName: '',
      accountStatus: '',
      accountType: '',
      brokerCode: '',
      externalAccountCode: '',
    },
    mode: 'onBlur',
    resolver: yupResolver(schema),
  })

  const mutateAccountAdd = useMutation({
    mutationFn: CustomerAccountServices.addAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['account-list'],
      })
      showToast('Account Added', `You have added a new account.`, 'success')
      onModalClose()
    },
    onError: (err) => {
      showToast(`Add Account Failed`, err.message, 'danger')
    },
  })

  const mutateAccountEdit = useMutation({
    mutationFn: CustomerAccountServices.editAccount,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['account-list'],
      })
      showToast(
        'Account Updated',
        `You have updated an account - ${accountId}`,
        'success',
      )
      onModalClose()
    },
    onError: (err) => {
      showToast(`Update Account Failed`, err.message, 'danger')
    },
  })

  const { data: accountDetailData } = useQuery({
    enabled: Boolean(accountId),
    queryFn: () =>
      CustomerAccountServices.getAccountDetail({
        accountId: accountId!,
      }),
    queryKey: ['account-detail', accountId],
  })

  const { data: brokersData } = useQuery({
    queryFn: () =>
      CommonServices.getEnumeration({
        codeList: ['BROKER'],
      }),
    queryKey: ['brokers'],
  })
  const brokerList = brokersData?.list[0]?.list ?? []
  const brokerOptions: SelectProps<object>['options'] = brokerList.map(
    (broker) => ({
      item: broker,
      label: broker.desc,
      value: broker.value,
    }),
  )

  // Once accountDetailData is fetched, update default values
  useEffect(() => {
    if (accountDetailData?.AccountDetail) {
      reset({
        accountCode: accountDetailData.AccountDetail.accountCode,
        accountName: accountDetailData.AccountDetail.accountName,
        accountStatus: accountDetailData.AccountDetail.accountStatus,
        accountType: accountDetailData.AccountDetail.accountType,
        brokerCode: accountDetailData.AccountDetail.brokerCode,
        externalAccountCode:
          accountDetailData.AccountDetail.externalAccountCode,
      })
    }
  }, [accountDetailData, reset])

  const onBack = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        addEditAccountType: undefined,
        addEditMode: undefined,
      }
    })
    reset({
      accountCode: '',
      accountName: '',
      accountStatus: '',
      accountType: '',
      brokerCode: '',
      externalAccountCode: '',
    })
  }

  const onModalClose = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        accountId: undefined,
        addEditAccountType: undefined,
        addEditMode: undefined,
      }
    })
    reset({
      accountCode: '',
      accountName: '',
      accountStatus: '',
      accountType: '',
      brokerCode: '',
      externalAccountCode: '',
    })
  }

  const onSubmit = (data: FormData) => {
    if (addEditMode === 'ADD') {
      const payload = {
        accountCode: data.accountCode,
        accountName: data.accountName,
        accountStatus: data.accountStatus as AccountStatus,
        accountType: data.accountType as AccountType,
        brokerCode: data.brokerCode,
        customerId,
        externalAccountCode: data.externalAccountCode,
      }
      mutateAccountAdd.mutateAsync(payload)
    } else if (addEditMode === 'EDIT' && accountId) {
      const payload = {
        accountCode: data.accountCode,
        accountId,
        accountName: data.accountName,
        accountStatus: data.accountStatus as AccountStatus,
        accountType: data.accountType as AccountType,
        brokerCode: data.brokerCode,
        customerId,
        externalAccountCode: data.externalAccountCode,
      }
      mutateAccountEdit.mutateAsync(payload)
    }
  }

  const getModalTitle = () => {
    return addEditMode === 'ADD' ? 'Add Account' : 'Edit Account'
  }

  return (
    <AppModal
      onBack={addEditMode === 'EDIT' ? onBack : undefined}
      onCancel={onModalClose}
      open={Boolean(addEditMode) && addEditAccountType === 'ACCOUNT'}
      stickyFooter={
        <div className="grid grid-cols-2 gap-2">
          <AppButton cosmeticType="skeleton" label="Cancel" onClick={onBack} />
          <AppButton
            cosmeticType="secondary"
            disabled={
              !isValid ||
              mutateAccountAdd.isPending ||
              mutateAccountEdit.isPending
            }
            label={addEditMode === 'EDIT' ? 'Edit' : 'Add'}
            onClick={handleSubmit(onSubmit)}
          />
        </div>
      }
      titleDescription={addEditMode === 'EDIT' ? `- ${accountId}` : ''}
      titleLabel={getModalTitle()}
      width={720}
    >
      <AppInfoCardVariant9
        containerClassName="mb-3"
        contents={[
          {
            label: 'Customer ID',
            value: customerId,
          },
          {
            label: 'Customer Name',
            value: customerName,
          },
        ]}
      />
      <MeritDivider />

      <form
        className="mt-3 flex flex-col gap-3"
        onSubmit={handleSubmit(onSubmit)}
      >
        <AppNormalInput
          label="Account Code"
          {...register('accountCode')}
          error={errors.accountCode?.message}
          placeholder="Input account ID"
          required
        />

        <div className="grid grid-cols-2 gap-2">
          <AppNormalInput
            label="Account Name"
            {...register('accountName')}
            error={errors.accountName?.message}
            placeholder="Input account name"
            required
          />

          <ControlledAppSelectInput
            control={control}
            label="Account Type"
            name="accountType"
            options={AccountTypeOptions}
            placeholder="Select account type"
            required
          />
        </div>

        <ControlledAppChoiceInput
          cols={2}
          control={control}
          label="Status"
          name="accountStatus"
          options={AccountStatusOptions}
          required
        />

        <ControlledAppSelectInput
          control={control}
          label="Broker Code"
          name="brokerCode"
          options={brokerOptions}
          placeholder="Select broker code"
          required
        />

        <AppNormalInput
          label="External Account Code"
          {...register('externalAccountCode')}
          error={errors.externalAccountCode?.message}
          placeholder="Input external account code"
          required
        />

        <RequiredIndicator />
      </form>
    </AppModal>
  )
}
