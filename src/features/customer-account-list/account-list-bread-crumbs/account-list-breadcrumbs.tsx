import { ReactSVG } from 'react-svg'
import { AppBreadcrumb } from '@/features/shared/ui'
import {
  lightBulbIcon,
  listDashesIcon,
  UsersFillIcon,
} from '@/features/shared/ui/app-icons'

export function AccountListBreadcrumbs() {
  return (
    <div className="mb-4">
      <AppBreadcrumb
        breadcrumbs={[
          {
            icon: <UsersFillIcon className="fill-blue-green" size="14" />,
            label: 'Customer Management',
            to: '/admin-panel/account-management',
          },
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={lightBulbIcon}
              />
            ),
            label: 'Customer List',
          },
          {
            icon: (
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={listDashesIcon}
              />
            ),
            label: 'Account List',
          },
        ]}
      />
    </div>
  )
}
