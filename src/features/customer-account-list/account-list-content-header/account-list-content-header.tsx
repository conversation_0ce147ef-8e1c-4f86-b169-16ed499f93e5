import { useNavigate } from 'react-router-dom'
import { Dropdown, type MenuProps } from 'antd'
import { ReactSVG } from 'react-svg'
import { AppButton, ContentHeader } from '@/features/shared/ui'
import {
  arrowsLeftRightIcon,
  caretDownIcon,
  plusIcon,
} from '@/features/shared/ui/app-icons'
import { usePermission } from '@/features/shared/utils'

const ADD_PERMISSION = 400100
const TRANSFER_PERMISSION = 400100

const items: MenuProps['items'] = [
  {
    key: 'add-account',
    label: (
      <AppButton
        cosmeticType="transparent"
        label="Add Account"
        prefixIcon={
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title')
            }}
            src={plusIcon}
          />
        }
        size="s"
      />
    ),
  },
  {
    key: 'add-subaccount',
    label: (
      <AppButton
        cosmeticType="transparent"
        label="Add Sub Account"
        prefixIcon={
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title')
            }}
            src={plusIcon}
          />
        }
        size="s"
      />
    ),
  },
]

interface AccountListContentHeaderProps {
  onAddAccount: () => void
  onAddSubAccount: () => void
  onTransferCash: () => void
}

export function AccountListContentHeader(props: AccountListContentHeaderProps) {
  const { onAddAccount, onAddSubAccount, onTransferCash } = props
  const navigate = useNavigate()
  const { hasPermission } = usePermission()

  const handleBack = () => {
    navigate('/admin-panel/customer-management/list')
  }

  const onClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      case 'add-account':
        onAddAccount()
        break
      case 'add-subaccount':
        onAddSubAccount()
        break
      default:
        break
    }
  }

  return (
    <ContentHeader onBack={handleBack} title="Account List">
      {hasPermission(TRANSFER_PERMISSION) && (
        <AppButton
          label="Transfer Cash"
          onClick={onTransferCash}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={arrowsLeftRightIcon}
            />
          }
        />
      )}
      {hasPermission(ADD_PERMISSION) && (
        <Dropdown menu={{ items, onClick }} placement="bottomRight">
          <AppButton
            className="mr-2 min-w-[120px]"
            label="Add Account"
            prefixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={plusIcon}
              />
            }
            suffixIcon={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={caretDownIcon}
              />
            }
          />
        </Dropdown>
      )}
    </ContentHeader>
  )
}
