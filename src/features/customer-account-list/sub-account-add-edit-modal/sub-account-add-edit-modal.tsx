import { useRecoilState } from 'recoil'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useForm } from 'react-hook-form'
import { ReactSVG } from 'react-svg'
import { useMutation, useQuery } from '@tanstack/react-query'
import { type SelectProps } from 'antd'
import { useEffect } from 'react'
import { customerAccountListState } from '@/features/shared/states/customer'
import {
  AppButton,
  AppInfoCardVariant9,
  AppModal,
  AppNormalInput,
  ControlledAppChoiceInput,
  ControlledAppSelectInput,
  MeritDivider,
  RequiredIndicator,
} from '@/features/shared/ui'
import { warningCircleFilledIcon } from '@/features/shared/ui/app-icons'
import {
  AccountStatusOptions,
  AccountTypeOptions,
} from '@/features/shared/mappings/customer-account'
import {
  CustomerAccountServices,
  CommonServices,
  type AccountType,
  type AccountStatus,
} from '@/features/shared/services'
import { useToast } from '@/features/shared/utils'

const schema = yup
  .object()
  .shape({
    accountCode: yup.string().required(),
    accountName: yup.string().required('Account Name is a required field'),
    accountStatus: yup.string().required(),
    accountType: yup.string().required(),
    brokerCode: yup.string().required(),
    externalAccountCode: yup.string().required(),
    parentAccountId: yup.string().required(),
  })
  .required()

type FormData = yup.InferType<typeof schema>

export function SubAccountAddEditModal() {
  const { showToast } = useToast()
  const [
    {
      accountId,
      addEditAccountType,
      addEditMode,
      customerId,
      customerName,
      parentAccountList,
      subAccount,
    },
    setCustomerAccountList,
  ] = useRecoilState(customerAccountListState)

  const {
    control,
    formState: { isValid, errors },
    handleSubmit,
    register,
    reset,
    watch,
  } = useForm<FormData>({
    defaultValues: {
      accountCode: subAccount?.accountCode ?? '',
      accountName: subAccount?.accountName ?? '',
      accountStatus: subAccount?.accountStatus ?? '',
      accountType: subAccount?.accountType ?? '',
      brokerCode: subAccount?.brokerCode ?? '',
      externalAccountCode: subAccount?.externalAccountCode ?? '',
      parentAccountId: accountId ? String(accountId) : '',
    },
    mode: 'onBlur',
    resolver: yupResolver(schema),
  })
  const watchedParentAccountId = watch('parentAccountId')

  const mutateSubAccountAdd = useMutation({
    mutationFn: CustomerAccountServices.addAccount,
    onSuccess: () => {
      showToast(
        'Account Added',
        `You have added a new sub account under account ${watchedParentAccountId}.`,
        'success',
      )
      onModalClose()
    },
  })

  const mutateSubAccountEdit = useMutation({
    mutationFn: CustomerAccountServices.editAccount,
    onSuccess: () => {
      showToast(
        'Account Updated',
        `You have updated a sub account - ${subAccount?.accountId} - under account - ${subAccount?.parentAccountID}`,
        'success',
      )
      onModalClose()
    },
  })

  const { data: brokersData } = useQuery({
    queryFn: () =>
      CommonServices.getEnumeration({
        codeList: ['BROKER'],
      }),
    queryKey: ['brokers'],
  })
  const brokerList = brokersData?.list[0]?.list ?? []
  const brokerOptions: SelectProps<object>['options'] = brokerList.map(
    (broker) => ({
      item: broker,
      label: broker.desc,
      value: broker.value,
    }),
  )

  const parentAccountListOptions: SelectProps<object>['options'] =
    parentAccountList.map((account) => ({
      label: `${account.accountId} - ${account.accountName}`,
      value: String(account.accountId),
    }))

  const onBack = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        addEditAccountType: undefined,
        addEditMode: undefined,
        subAccount: undefined,
      }
    })
    reset({
      accountCode: '',
      accountName: '',
      accountStatus: '',
      accountType: '',
      brokerCode: '',
      externalAccountCode: '',
      parentAccountId: '',
    })
  }

  const onModalClose = () => {
    setCustomerAccountList((prev) => {
      return {
        ...prev,
        accountId: undefined,
        addEditAccountType: undefined,
        addEditMode: undefined,
        subAccount: undefined,
      }
    })
    reset({
      accountCode: '',
      accountName: '',
      accountStatus: '',
      accountType: '',
      brokerCode: '',
      externalAccountCode: '',
      parentAccountId: '',
    })
  }

  const onSubmit = (data: FormData) => {
    if (addEditMode === 'ADD') {
      const payload = {
        accountCode: data.accountCode,
        accountName: data.accountName,
        accountStatus: data.accountStatus as AccountStatus,
        accountType: data.accountType as AccountType,
        brokerCode: data.brokerCode,
        customerId,
        externalAccountCode: data.externalAccountCode,
        parentAccountID: Number(data.parentAccountId),
      }
      mutateSubAccountAdd.mutateAsync(payload)
    } else if (addEditMode === 'EDIT' && subAccount?.accountId) {
      const payload = {
        accountCode: data.accountCode,
        accountId: subAccount.accountId,
        accountName: data.accountName,
        accountStatus: data.accountStatus as AccountStatus,
        accountType: data.accountType as AccountType,
        brokerCode: data.brokerCode,
        externalAccountCode: data.externalAccountCode,
        parentAccountID: Number(data.parentAccountId),
      }
      mutateSubAccountEdit.mutateAsync(payload)
    }
  }

  const getModalTitle = () => {
    return addEditMode === 'ADD' ? 'Add Sub Account' : 'Edit Sub Account'
  }

  // Initialize form values
  useEffect(() => {
    reset({
      accountCode: subAccount?.accountCode ?? '',
      accountName: subAccount?.accountName ?? '',
      accountStatus: subAccount?.accountStatus ?? '',
      accountType: subAccount?.accountType ?? '',
      brokerCode: subAccount?.brokerCode ?? '',
      externalAccountCode: subAccount?.externalAccountCode ?? '',
      parentAccountId: accountId ? String(accountId) : '',
    })
  }, [subAccount, accountId, reset])

  return (
    <AppModal
      onBack={addEditMode === 'EDIT' ? onBack : undefined}
      onCancel={onModalClose}
      open={Boolean(addEditMode) && addEditAccountType === 'SUB_ACCOUNT'}
      stickyFooter={
        <div className="grid grid-cols-2 gap-2">
          <AppButton cosmeticType="skeleton" label="Cancel" onClick={onBack} />
          <AppButton
            cosmeticType="secondary"
            disabled={
              !isValid ||
              mutateSubAccountAdd.isPending ||
              mutateSubAccountEdit.isPending
            }
            label={addEditMode === 'EDIT' ? 'Edit' : 'Add'}
            onClick={handleSubmit(onSubmit)}
          />
        </div>
      }
      titleDescription={
        addEditMode === 'EDIT' ? `- ${subAccount?.accountId}` : ''
      }
      titleLabel={getModalTitle()}
      width={720}
    >
      <AppInfoCardVariant9
        containerClassName="mb-3"
        contents={[
          {
            label: 'Customer ID',
            value: customerId,
          },
          {
            label: 'Customer Name',
            value: customerName,
          },
          addEditMode === 'EDIT'
            ? {
                label: 'Parent Account ID',
                value: subAccount?.parentAccountID,
              }
            : null,
        ]}
      />
      <MeritDivider />

      <form
        className="mt-3 flex flex-col gap-3"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div>
          <ControlledAppSelectInput
            control={control}
            label="Parent Account ID"
            name="parentAccountId"
            options={parentAccountListOptions}
            placeholder="Select parent account code"
            required
          />
          {addEditMode === 'ADD' && (
            <div className="mt-2 flex items-center gap-1 bg-bg-caution p-2 font-light leading-4">
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-caution', 'w-3', 'h-3')
                }}
                src={warningCircleFilledIcon}
              />
              <span className="text-txt-caution">
                This field will not be editable once it is created.
              </span>
            </div>
          )}
        </div>

        <div className="grid grid-cols-2 gap-2">
          <AppNormalInput
            label="Account Code"
            {...register('accountCode')}
            error={errors.accountCode?.message}
            placeholder="Input account ID"
            required
          />

          <AppNormalInput
            label="Sub Account Name"
            {...register('accountName')}
            error={errors.accountName?.message}
            placeholder="Input account name"
            required
          />
        </div>

        <ControlledAppSelectInput
          control={control}
          label="Account Type"
          name="accountType"
          options={AccountTypeOptions}
          placeholder="Select account type"
          required
        />

        <ControlledAppChoiceInput
          cols={2}
          control={control}
          label="Status"
          name="accountStatus"
          options={AccountStatusOptions}
          required
        />

        <ControlledAppSelectInput
          control={control}
          label="Broker Code"
          name="brokerCode"
          options={brokerOptions}
          placeholder="Select broker code"
          required
        />

        <AppNormalInput
          label="External Account Code"
          {...register('externalAccountCode')}
          error={errors.externalAccountCode?.message}
          placeholder="Input external account code"
          required
        />

        <RequiredIndicator />
      </form>
    </AppModal>
  )
}
