import { useRecoilValue } from 'recoil'
import { AppInfoCardVariant9 } from '@/features/shared/ui'
import { customerAccountListState } from '@/features/shared/states/customer'
import { mapRiskLevelColor } from '@/features/shared/utils'

function Divider() {
  return <div className="h-2 w-[0.5px] bg-txt-inactive" />
}

export function AccountListSummary() {
  const {
    customerId,
    customerName,
    riskLevel,
    activeAccounts,
    inactiveAccounts,
  } = useRecoilValue(customerAccountListState)

  return (
    <div className="grid grid-cols-[1fr,auto,1fr,auto] items-center gap-x-7 gap-y-2 px-3 py-2 lg:grid-cols-[1fr,auto,1fr,auto,1fr,auto] xl:grid-cols-[1fr,auto,1fr,auto,1fr,auto,1fr,auto,1fr,auto]">
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Customer ID',
            value: customerId || '--',
          },
        ]}
      />
      <Divider />
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Customer Name',
            value: customerName || '--',
          },
        ]}
      />
      <Divider />
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Risk Level',
            value: (
              <span
                style={{
                  color: mapRiskLevelColor(riskLevel as RiskLevel),
                }}
              >
                {riskLevel || '--'}
              </span>
            ),
          },
        ]}
      />
      <Divider />
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Active Accounts',
            value: activeAccounts || '--',
            valueColor: activeAccounts ? 'rgb(var(--color-txt-positive))' : '',
          },
        ]}
      />
      <Divider />
      <AppInfoCardVariant9
        contents={[
          {
            label: 'Inactive Accounts',
            value: inactiveAccounts || '--',
            valueColor: inactiveAccounts ? 'rgb(var(--color-txt-caution))' : '',
          },
        ]}
      />
    </div>
  )
}
