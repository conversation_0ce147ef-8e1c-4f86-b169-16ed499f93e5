import dayjs from 'dayjs'
import { useParams, useSearchParams } from 'react-router-dom'
import type { TableProps } from 'antd'
import { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSetRecoilState } from 'recoil'
import { AppTable, CellWithStatusIndicator } from '@/features/shared/ui'
// import { convertSortTypeToSortOrder } from '@/features/shared/utils'
import {
  type ParentAccount,
  CustomerAccountServices,
  type AccountStatus,
  type CustomerAccount,
  type AccountType,
} from '@/features/shared/services/customer-account-services'
import {
  AccountStatuses,
  AccountTypes,
} from '@/features/shared/mappings/customer-account'
import { customerAccountListState } from '@/features/shared/states'

const SortByOptions = {
  ACCOUNT_ID: {
    key: 'ACCOUNT_ID',
  },
  CREATE_TIME: {
    key: 'CREATE_TIME',
  },
  UPDATE_TIME: {
    key: 'UPDATE_TIME',
  },
}

interface CustomerListTableProps {
  onRowClick: (record: CustomerAccount) => void
}

export function AccountListTable(props: CustomerListTableProps) {
  const { onRowClick } = props
  const setCustomerAccountDetails = useSetRecoilState(customerAccountListState)

  const { id } = useParams()

  const [searchParams] = useSearchParams()
  const sortByQuery = searchParams.get('sortBy')
  const sortTypeQuery = searchParams.get('sortType')

  const { data, isLoading } = useQuery({
    enabled: Boolean(id),
    queryFn: () =>
      CustomerAccountServices.getAccountList({ customerId: Number(id) }),
    queryKey: ['account-list', id],
  })

  const accountList = useMemo(() => data?.accountList ?? [], [data])

  const [dataSource, setDataSource] = useState<CustomerAccount[]>(accountList)

  const accountListMinimal = dataSource.map(
    (account: CustomerAccount): ParentAccount => {
      return {
        accountCode: account.accountCode,
        accountId: account.accountId,
        accountName: account.accountName,
      }
    },
  )

  useEffect(() => {
    const accountExpandList = accountList.map((account: CustomerAccount) => {
      if (account.subAccountCount > 0) {
        return {
          ...account,
          children: [],
        }
      }
      return account
    })
    setDataSource(accountExpandList)
  }, [accountList])

  const handleExpand = async (expanded: boolean, record: CustomerAccount) => {
    if (expanded) {
      if (
        record.subAccountCount > 0 &&
        record.children &&
        record.children.length === 0
      ) {
        const res = await CustomerAccountServices.getAccountDetail({
          accountId: record.accountId,
        })
        const updatedDataSource = dataSource.map((item: CustomerAccount) =>
          item.accountId === record.accountId
            ? { ...item, children: res.subAccountList }
            : item,
        )
        setDataSource(updatedDataSource)
      }
    }
  }

  if (data) {
    const { customerName, riskLevel, activeAccounts, inactiveAccounts } = data
    setCustomerAccountDetails((prev) => {
      return {
        ...prev,
        activeAccounts,
        customerId: Number(id),
        customerName,
        inactiveAccounts,
        parentAccountList: accountListMinimal,
        riskLevel,
      }
    })
  }

  const columns: TableProps<CustomerAccount>['columns'] = useMemo(
    () => [
      {
        dataIndex: 'accountId',
        key: SortByOptions.ACCOUNT_ID.key,
        title: 'Account ID',
        // sorter: true,
        // sortOrder:
        //   sortByQuery === SortByOptions.ACCOUNT_ID.key
        //     ? convertSortTypeToSortOrder(sortTypeQuery)
        //     : undefined,
      },
      {
        dataIndex: 'accountCode',
        title: 'Account Code',
      },
      {
        dataIndex: 'accountName',
        title: 'Account Name',
      },
      {
        dataIndex: 'accountType',
        render: (accountType: string) =>
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          AccountTypes[accountType as AccountType].label ?? accountType,
        title: 'Account Type',
      },
      {
        dataIndex: 'accountStatus',
        render: (status: string) =>
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          AccountStatuses[status as AccountStatus] ? (
            <CellWithStatusIndicator
              color={AccountStatuses[status as AccountStatus].color}
              text={AccountStatuses[status as AccountStatus].label}
            />
          ) : (
            status
          ),
        title: 'Status',
      },
      // NOTE: https://siriustech.atlassian.net/browse/MERIT-3192
      // {
      //   dataIndex: 'currency',
      //   title: 'Currency',
      // },
      // {
      //   dataIndex: 'amount',
      //   render: (value: number) => (
      //     <span>{value ? numeral(value).format('0,0.00') : '--'}</span>
      //   ),
      //   title: 'Amount',
      // },
      {
        dataIndex: 'brokerCode',
        title: 'Broker Code',
      },
      {
        dataIndex: 'brokerName',
        title: 'Broker Name',
      },
      {
        dataIndex: 'externalAccountCode',
        title: 'External Account Code',
      },
      {
        dataIndex: 'createdAt',
        key: SortByOptions.CREATE_TIME.key,
        // sorter: true,
        // sortOrder:
        //   sortByQuery === SortByOptions.CREATE_TIME.key
        //     ? convertSortTypeToSortOrder(sortTypeQuery)
        //     : undefined,
        render: (createTime: string) => (
          <span>
            {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
          </span>
        ),

        title: 'Created Time',
      },
      {
        dataIndex: 'updatedOn',
        key: SortByOptions.UPDATE_TIME.key,
        // sorter: true,
        // sortOrder:
        //   sortByQuery === SortByOptions.UPDATE_TIME.key
        //     ? convertSortTypeToSortOrder(sortTypeQuery)
        //     : undefined,
        render: (updateTime: string) => (
          <span>
            {updateTime ? dayjs(updateTime).format('DD-MM-YYYY HH:mm') : '--'}
          </span>
        ),

        title: 'Updated Time',
      },
    ],
    [sortByQuery, sortTypeQuery],
  )

  return (
    <AppTable
      columns={columns}
      dataSource={dataSource}
      expandable={{
        onExpand: handleExpand,
        rowExpandable: (record) => record.subAccountCount !== 0,
      }}
      loading={isLoading}
      onRow={(record) => ({
        onClick: () => {
          onRowClick(record)
        },
      })}
      rowKey="accountId"
      total={dataSource.length}
    />
  )
}
