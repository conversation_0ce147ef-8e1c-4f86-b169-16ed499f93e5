import { Editor } from '@tiptap/react'
import { useCallback } from 'react'

interface TiptapToolbarProps {
  editor: Editor
  onImageUpload: () => void
  onVideoInsert: () => void
  onLinkInsert: () => void
  onTableInsert: () => void
}

export function TiptapToolbar({ editor, onImageUpload, onVideoInsert, onLinkInsert, onTableInsert }: TiptapToolbarProps) {
  const setHeading = useCallback((level: 1 | 2 | 3 | 4 | 5 | 6) => {
    editor.chain().focus().toggleHeading({ level }).run()
  }, [editor])

  const setFontSize = useCallback((size: string) => {
    editor.chain().focus().setFontFamily(size).run()
  }, [editor])

  const setTextColor = useCallback((color: string) => {
    editor.chain().focus().setColor(color).run()
  }, [editor])

  const setHighlight = useCallback((color: string) => {
    editor.chain().focus().setHighlight({ color }).run()
  }, [editor])

  const setAlignment = useCallback((alignment: 'left' | 'center' | 'right' | 'justify') => {
    editor.chain().focus().setTextAlign(alignment).run()
  }, [editor])

  return (
    <div className="tiptap-toolbar">
      {/* 标题选择 */}
      <div className="toolbar-group">
        <select 
          onChange={(e) => {
            const value = e.target.value
            if (value === 'paragraph') {
              editor.chain().focus().setParagraph().run()
            } else {
              const level = parseInt(value) as 1 | 2 | 3 | 4 | 5 | 6
              setHeading(level)
            }
          }}
          value={
            editor.isActive('heading', { level: 1 }) ? '1' :
            editor.isActive('heading', { level: 2 }) ? '2' :
            editor.isActive('heading', { level: 3 }) ? '3' :
            editor.isActive('heading', { level: 4 }) ? '4' :
            editor.isActive('heading', { level: 5 }) ? '5' :
            editor.isActive('heading', { level: 6 }) ? '6' : 'paragraph'
          }
        >
          <option value="paragraph">Normal</option>
          <option value="1">Heading 1</option>
          <option value="2">Heading 2</option>
          <option value="3">Heading 3</option>
          <option value="4">Heading 4</option>
          <option value="5">Heading 5</option>
          <option value="6">Heading 6</option>
        </select>
      </div>

      {/* 字体样式 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'active' : ''}
          title="Bold"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'active' : ''}
          title="Italic"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'active' : ''}
          title="Underline"
        >
          <u>U</u>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={editor.isActive('strike') ? 'active' : ''}
          title="Strike"
        >
          <s>S</s>
        </button>
      </div>

      {/* 上标下标 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleSubscript().run()}
          className={editor.isActive('subscript') ? 'active' : ''}
          title="Subscript"
        >
          X₂
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleSuperscript().run()}
          className={editor.isActive('superscript') ? 'active' : ''}
          title="Superscript"
        >
          X²
        </button>
      </div>

      {/* 颜色选择 */}
      <div className="toolbar-group">
        <input
          type="color"
          onChange={(e) => setTextColor(e.target.value)}
          title="Text Color"
          className="color-picker"
        />
        <input
          type="color"
          onChange={(e) => setHighlight(e.target.value)}
          title="Highlight Color"
          className="color-picker"
        />
      </div>

      {/* 列表 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive('bulletList') ? 'active' : ''}
          title="Bullet List"
        >
          •
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive('orderedList') ? 'active' : ''}
          title="Ordered List"
        >
          1.
        </button>
      </div>

      {/* 对齐 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => setAlignment('left')}
          className={editor.isActive({ textAlign: 'left' }) ? 'active' : ''}
          title="Align Left"
        >
          ⬅
        </button>
        <button
          type="button"
          onClick={() => setAlignment('center')}
          className={editor.isActive({ textAlign: 'center' }) ? 'active' : ''}
          title="Align Center"
        >
          ↔
        </button>
        <button
          type="button"
          onClick={() => setAlignment('right')}
          className={editor.isActive({ textAlign: 'right' }) ? 'active' : ''}
          title="Align Right"
        >
          ➡
        </button>
      </div>

      {/* 引用和代码 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={editor.isActive('blockquote') ? 'active' : ''}
          title="Blockquote"
        >
          "
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={editor.isActive('codeBlock') ? 'active' : ''}
          title="Code Block"
        >
          {'{}'}
        </button>
      </div>

      {/* 表格操作 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={onTableInsert}
          title="Insert Table"
        >
          📊
        </button>
        {editor.isActive('table') && (
          <>
            <button
              type="button"
              onClick={() => editor.chain().focus().addColumnBefore().run()}
              title="Add Column Before"
            >
              ⬅+
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().addColumnAfter().run()}
              title="Add Column After"
            >
              +➡
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().deleteColumn().run()}
              title="Delete Column"
            >
              ❌📋
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().addRowBefore().run()}
              title="Add Row Before"
            >
              ⬆+
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().addRowAfter().run()}
              title="Add Row After"
            >
              +⬇
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().deleteRow().run()}
              title="Delete Row"
            >
              ❌📄
            </button>
            <button
              type="button"
              onClick={() => editor.chain().focus().deleteTable().run()}
              title="Delete Table"
            >
              🗑📊
            </button>
          </>
        )}
      </div>

      {/* 媒体插入 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={onLinkInsert}
          className={editor.isActive('link') ? 'active' : ''}
          title="Insert Link"
        >
          🔗
        </button>
        <button
          type="button"
          onClick={onImageUpload}
          title="Insert Image"
        >
          🖼
        </button>
        <button
          type="button"
          onClick={onVideoInsert}
          title="Insert Video"
        >
          📹
        </button>
      </div>

      {/* 清除格式 */}
      <div className="toolbar-group">
        <button
          type="button"
          onClick={() => editor.chain().focus().clearNodes().unsetAllMarks().run()}
          title="Clear Format"
        >
          🧹
        </button>
      </div>
    </div>
  )
}