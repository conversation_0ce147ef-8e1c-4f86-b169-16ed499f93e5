import { forwardRef, useEffect, useImperativeHandle, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, Editor<PERSON>ontent, Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Color from '@tiptap/extension-color'
import TextAlign from '@tiptap/extension-text-align'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Highlight from '@tiptap/extension-highlight'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Youtube from '@tiptap/extension-youtube'
import CharacterCount from '@tiptap/extension-character-count'
import Placeholder from '@tiptap/extension-placeholder'
// import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import { ReactSVG } from 'react-svg'
import { formInvalidIcon } from '../../app-icons'
import { TiptapToolbar } from './tiptap-toolbar'
import './app-tiptap-editor.css'
import { TextStyle } from '@tiptap/extension-text-style'
import { Table } from '@tiptap/extension-table'

interface AppTiptapEditorProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  error?: string
  wrapperClassName?: string
  required?: boolean
  disabled?: boolean
  maxLength?: number
}

export interface AppTiptapEditorRef {
  focus: () => void
  blur: () => void
  getEditor: () => Editor | null
}

function AppTiptapEditorInner(
  props: AppTiptapEditorProps,
  ref: React.ForwardedRef<AppTiptapEditorRef>
) {
  const {
    label,
    value = '',
    onChange,
    onBlur,
    placeholder = 'Enter article content...',
    error,
    wrapperClassName,
    required,
    disabled,
    maxLength = 10000
  } = props

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6]
        }
      }),
      TextStyle,
      Color,
      TextAlign.configure({
        types: ['heading', 'paragraph']
      }),
      FontFamily,
      Underline,
      Subscript,
      Superscript,
      Highlight.configure({
        multicolor: true
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'tiptap-link'
        }
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'tiptap-image'
        }
      }),
      Youtube.configure({
        width: 640,
        height: 480,
        HTMLAttributes: {
          class: 'tiptap-youtube'
        }
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'tiptap-table'
        }
      }),
      TableRow,
      TableHeader,
      TableCell,
      CharacterCount.configure({
        limit: maxLength
      }),
      Placeholder.configure({
        placeholder
      })
    ],
    content: value,
    editable: !disabled,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange?.(html)
    },
    onBlur: () => {
      onBlur?.()
    }
  })

  useImperativeHandle(ref, () => ({
    focus: () => editor?.commands.focus(),
    blur: () => editor?.commands.blur(),
    getEditor: () => editor
  }))

  // 当 value 从外部改变时更新编辑器内容
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value, { emitUpdate: false })
    }
  }, [editor, value])

  // 图片上传处理
  const handleImageUpload = useCallback(() => {
    const input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('accept', 'image/*')
    input.click()

    input.onchange = async () => {
      const file = input.files?.[0]
      if (file && editor) {
        try {
          // 临时使用本地预览，实际项目中应该上传到服务器
          const reader = new FileReader()
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string
            editor.chain().focus().setImage({ src: imageUrl }).run()
          }
          reader.readAsDataURL(file)
        } catch (error) {
          console.error('Image upload failed:', error)
        }
      }
    }
  }, [editor])

  // 视频插入处理
  const handleVideoInsert = useCallback(() => {
    const url = prompt('Enter YouTube URL:')
    if (url && editor) {
      editor.commands.setYoutubeVideo({
        src: url,
        width: 640,
        height: 480
      })
    }
  }, [editor])

  // 链接插入处理
  const handleLinkInsert = useCallback(() => {
    const url = prompt('Enter URL:')
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }, [editor])

  // 表格插入处理
  const handleTableInsert = useCallback(() => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    }
  }, [editor])

  if (!editor) {
    return null
  }

  const characterCount = editor.storage.characterCount.characters()
  const isOverLimit = characterCount > maxLength

  return (
    <div className={wrapperClassName}>
      <label className="block text-xs font-semibold capitalize not-italic text-txt-title">
        {label}
        {required && (
          <span className="ml-1 inline-block text-xs font-semibold capitalize not-italic text-txt-negative">
            *
          </span>
        )}
      </label>
      
      <div className="mt-2">
        <div className={`tiptap-editor ${error ? 'error' : ''} ${disabled ? 'disabled' : ''} ${isOverLimit ? 'over-limit' : ''}`}>
          <TiptapToolbar 
            editor={editor}
            onImageUpload={handleImageUpload}
            onVideoInsert={handleVideoInsert}
            onLinkInsert={handleLinkInsert}
            onTableInsert={handleTableInsert}
          />
          <EditorContent editor={editor} className="tiptap-content" />
        </div>
        
        {/* 字符计数和错误信息 */}
        <div className="mt-1 flex justify-between items-center">
          <div>
            {error && (
              <p className="flex items-center text-xs normal-case text-txt-negative">
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-negative')
                  }}
                  className="mr-1"
                  src={formInvalidIcon}
                />
                {error}
              </p>
            )}
          </div>
          <span className={`text-xs ${isOverLimit ? 'text-txt-negative' : 'text-txt-inactive'}`}>
            {characterCount}/{maxLength}
          </span>
        </div>
      </div>
    </div>
  )
}

export const AppTiptapEditor = forwardRef(AppTiptapEditorInner)