/* Tiptap 编辑器样式 */
.tiptap-editor {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: white;
}

.tiptap-editor.error {
  border-color: #ef4444;
}

.tiptap-editor.disabled {
  background-color: #f5f5f5;
  opacity: 0.6;
}

.tiptap-editor.over-limit {
  border-color: #ef4444;
}

/* 工具栏样式 */
.tiptap-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.toolbar-group {
  display: flex;
  gap: 4px;
  align-items: center;
}

.toolbar-group:not(:last-child)::after {
  content: '';
  width: 1px;
  height: 20px;
  background-color: #d1d5db;
  margin-left: 4px;
}

.tiptap-toolbar button {
  padding: 4px 8px;
  border: 1px solid transparent;
  border-radius: 3px;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiptap-toolbar button:hover {
  background-color: #e5e7eb;
}

.tiptap-toolbar button.active {
  background-color: #3b82f6;
  color: white;
}

.tiptap-toolbar select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  background-color: white;
  font-size: 14px;
  min-width: 120px;
}

.color-picker {
  width: 28px;
  height: 28px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  cursor: pointer;
}

/* 编辑器内容样式 */
.tiptap-content {
  min-height: 200px;
}

.tiptap-content .ProseMirror {
  padding: 12px;
  min-height: 200px;
  line-height: 1.6;
  font-size: 14px;
  outline: none;
}

.tiptap-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 内容元素样式 */
.tiptap-content h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}

.tiptap-content h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.75em 0;
}

.tiptap-content h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin: 0.83em 0;
}

.tiptap-content h4 {
  font-size: 1em;
  font-weight: bold;
  margin: 1.12em 0;
}

.tiptap-content h5 {
  font-size: 0.83em;
  font-weight: bold;
  margin: 1.5em 0;
}

.tiptap-content h6 {
  font-size: 0.75em;
  font-weight: bold;
  margin: 1.67em 0;
}

.tiptap-content blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #6b7280;
}

.tiptap-content pre {
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
}

.tiptap-content code {
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.tiptap-content ul, .tiptap-content ol {
  padding-left: 24px;
  margin: 12px 0;
}

.tiptap-content li {
  margin: 4px 0;
}

.tiptap-link {
  color: #3b82f6;
  text-decoration: underline;
}

.tiptap-link:hover {
  color: #1d4ed8;
}

.tiptap-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.tiptap-youtube {
  margin: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tiptap-toolbar {
    padding: 4px;
    gap: 4px;
  }
  
  .toolbar-group {
    gap: 2px;
  }
  
  .tiptap-toolbar button {
    min-width: 24px;
    height: 24px;
    padding: 2px 4px;
    font-size: 12px;
  }
  
  .tiptap-toolbar select {
    min-width: 100px;
    font-size: 12px;
  }
}


/* 表格样式 */
.tiptap-table {
  border-collapse: collapse;
  margin: 16px 0;
  table-layout: fixed;
  width: 100%;
}

.tiptap-table td,
.tiptap-table th {
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  min-width: 1em;
  padding: 8px;
  position: relative;
  vertical-align: top;
}

.tiptap-table th {
  background-color: #f9fafb;
  font-weight: bold;
  text-align: left;
}

.tiptap-table .selectedCell:after {
  background: rgba(59, 130, 246, 0.1);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.tiptap-table .column-resize-handle {
  background-color: #3b82f6;
  bottom: -2px;
  position: absolute;
  right: -2px;
  top: 0;
  width: 4px;
  pointer-events: none;
}

.tiptap-table p {
  margin: 0;
}