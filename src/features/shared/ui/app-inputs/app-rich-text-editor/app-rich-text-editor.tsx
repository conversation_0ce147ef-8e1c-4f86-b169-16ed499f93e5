import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'
import ReactQuill from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import { ReactSVG } from 'react-svg'
import { formInvalidIcon } from '../../app-icons'
import './app-rich-text-editor.css'

interface AppRichTextEditorProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  error?: string
  wrapperClassName?: string
  required?: boolean
  disabled?: boolean
  maxLength?: number
}

export interface AppRichTextEditorRef {
  focus: () => void
  blur: () => void
  getEditor: () => ReactQuill | null
}

function AppRichTextEditorInner(
  props: AppRichTextEditorProps,
  ref: React.ForwardedRef<AppRichTextEditorRef>
) {
  const {
    label,
    value = '',
    onChange,
    onBlur,
    placeholder = 'Enter article content...',
    error,
    wrapperClassName,
    required,
    disabled,
    maxLength = 10000
  } = props

  const quillRef = useRef<ReactQuill>(null)

  useImperativeHandle(ref, () => ({
    focus: () => quillRef.current?.focus(),
    blur: () => quillRef.current?.blur(),
    getEditor: () => quillRef.current
  }))

  // 添加 useEffect 在这里
  useEffect(() => {
    // 只在初始化时设置值，避免每次 value 变化都重新设置
    if (value && quillRef.current) {
      const editor = quillRef.current.getEditor()
      if (editor && editor.getText().trim() === '') {
        editor.clipboard.dangerouslyPasteHTML(value)
      }
    }
  }, []) // 空依赖数组，只在组件挂载时执行

  // 富文本编辑器配置
  const modules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'script': 'sub' }, { 'script': 'super' }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'indent': '-1' }, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'align': [] }],
        ['blockquote', 'code-block'],
        ['link', 'image', 'video'],
        ['clean']
      ],
      handlers: {
        image: imageHandler,
        video: videoHandler
      }
    },
    clipboard: {
      matchVisual: false
    }
  }

  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'script',
    'list', 'bullet', 'indent',
    'direction', 'align',
    'blockquote', 'code-block',
    'link', 'image', 'video'
  ]

  // 图片上传处理
  function imageHandler() {
    const input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('accept', 'image/*')
    input.click()

    input.onchange = async () => {
      const file = input.files?.[0]
      if (file) {
        // 这里可以集成项目的文件上传服务
        // 示例：上传到服务器并获取URL
        try {
          const formData = new FormData()
          formData.append('file', file)
          
          // 调用项目的文件上传API
          // const response = await CommonServices.uploadFile(formData)
          // const imageUrl = response.fileUrl
          
          // 临时使用本地预览
          const reader = new FileReader()
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string
            const quill = quillRef.current?.getEditor()
            if (quill) {
              const range = quill.getSelection()
              quill.insertEmbed(range?.index || 0, 'image', imageUrl)
            }
          }
          reader.readAsDataURL(file)
        } catch (error) {
          console.error('Image upload failed:', error)
        }
      }
    }
  }

  // 视频处理
  function videoHandler() {
    const url = prompt('Enter video URL:')
    if (url) {
      const quill = quillRef.current?.getEditor()
      if (quill) {
        const range = quill.getSelection()
        quill.insertEmbed(range?.index || 0, 'video', url)
      }
    }
  }

  // 处理内容变化
  const handleChange = (content: string, delta: any, source: any, editor: any) => {
    const text = editor.getText()
    
    // 如果是用户输入且会超出限制，则阻止
    if (source === 'user' && text.length > maxLength) {
    // 获取当前选择位置
    const selection = editor.getSelection()
    // 撤销最后的操作
    editor.history.undo()
    // 恢复选择位置
    if (selection) {
      editor.setSelection(selection.index, 0)
    }
    return
  }
  
  onChange?.(content)
}

  // 获取当前文本长度
  const getCurrentTextLength = () => {
    const quill = quillRef.current?.getEditor()
    return quill?.getText().length || 0
  }

  return (
    <div className={wrapperClassName}>
      <label className="block text-xs font-semibold capitalize not-italic text-txt-title">
        {label}
        {required && (
          <span className="ml-1 inline-block text-xs font-semibold capitalize not-italic text-txt-negative">
            *
          </span>
        )}
      </label>
      
      <div className="mt-2">
        <div className={`rich-text-editor ${error ? 'error' : ''} ${disabled ? 'disabled' : ''}`}>
          <ReactQuill
            ref={quillRef}
            theme="snow"
            value={value}
            onChange={handleChange}
            onBlur={onBlur}
            placeholder={placeholder}
            modules={modules}
            formats={formats}
            readOnly={disabled}
            style={{
              backgroundColor: disabled ? '#f5f5f5' : 'white'
            }}
          />
        </div>
        
        {/* 字符计数 */}
        <div className="mt-1 flex justify-between items-center">
          <div>
            {error && (
              <p className="flex items-center text-xs normal-case text-txt-negative">
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-negative')
                  }}
                  className="mr-1"
                  src={formInvalidIcon}
                />
                {error}
              </p>
            )}
          </div>
          <span className="text-xs text-txt-inactive">
            {getCurrentTextLength()}/{maxLength}
          </span>
        </div>
      </div>
    </div>
  )
}

export const AppRichTextEditor = forwardRef(AppRichTextEditorInner)