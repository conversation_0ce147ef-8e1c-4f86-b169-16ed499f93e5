/* 富文本编辑器样式定制 */
.rich-text-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px;
  background-color: #f9fafb;
}

.rich-text-editor .ql-container {
  border: none;
  font-size: 14px;
  min-height: 200px;
}

.rich-text-editor .ql-editor {
  padding: 12px;
  min-height: 200px;
  line-height: 1.6;
}

.rich-text-editor .ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
}

/* 错误状态样式 */
.rich-text-editor.error .ql-toolbar,
.rich-text-editor.error .ql-container {
  border-color: #ef4444;
}

/* 禁用状态样式 */
.rich-text-editor.disabled .ql-toolbar {
  background-color: #f5f5f5;
}

.rich-text-editor.disabled .ql-editor {
  background-color: #f5f5f5;
  color: #6b7280;
}

/* 工具栏按钮样式 */
.rich-text-editor .ql-toolbar .ql-formats {
  margin-right: 8px;
}

.rich-text-editor .ql-toolbar button {
  padding: 4px;
  margin: 1px;
}

.rich-text-editor .ql-toolbar button:hover {
  background-color: #e5e7eb;
}

.rich-text-editor .ql-toolbar button.ql-active {
  background-color: #3b82f6;
  color: white;
}

/* 下拉菜单样式 */
.rich-text-editor .ql-toolbar .ql-picker {
  color: #374151;
}

.rich-text-editor .ql-toolbar .ql-picker-options {
  background-color: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rich-text-editor .ql-toolbar {
    padding: 4px;
  }
  
  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 4px;
  }
}