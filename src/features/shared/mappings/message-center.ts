import { ChannelType, type MessageStatus, type PublishChannel } from '../services'
import { type Mapping } from './type'

export const MessageStatusMapping: Mapping<MessageStatus> = {
  0: {
    key: 0,
    label: 'Draft',
    color: 'rgb(var(--color-txt-secondary))',
  },
  1: {
    key: 1,
    label: 'ToBeSend',
    color: 'rgb(var(--color-txt-positive-light))',
  },
  2: {
    key: 2,
    label: 'Sending',
    color: 'rgb(var(--color-txt-danger))',
  },
  3: {
    key: 3,
    label: 'Sent',
    color: 'rgb(var(--color-txt-positive-light))',
  },
  4: {
    key: 4,
    label: 'Partial Failure',
    color: 'rgb(var(--color-txt-danger))',
  },
  5: {
    key: 5,
    label: 'Full Failure',
    color: 'rgb(var(--color-txt-danger))',
  },
} as const

export const MESSAGE_STATUS_OPTIONS = [
  {
    label: MessageStatusMapping[0].label,
    value: MessageStatusMapping[0].key,
  },
  {
    label: MessageStatusMapping[1].label,
    value: MessageStatusMapping[1].key,
  },
  {
    label: MessageStatusMapping[2].label,
    value: MessageStatusMapping[2].key,
  },
]

export const REFINE_MESSAGE_STATUS_OPTIONS = [
  {
    label: 'All',
    value: 'all',
  },
  {
    label: MessageStatusMapping[0].label,
    labelColor: MessageStatusMapping[0].color,
    value: MessageStatusMapping[0].key,
  },
  {
    label: MessageStatusMapping[1].label,
    labelColor: MessageStatusMapping[1].color,
    value: MessageStatusMapping[1].key,
  },
  {
    label: MessageStatusMapping[2].label,
    labelColor: MessageStatusMapping[2].color,
    value: MessageStatusMapping[2].key,
  },
]

export const ChannelTypeOptions: Mapping<ChannelType> = {
  0: {
    key: 0,
    label: 'JPush',
  },
} as const

export const CHANNEL_TYPE_OPTIONS = [
  {
    label: ChannelTypeOptions[0].label,
    value: ChannelTypeOptions[0].key,
  },
]   