import { ArticlepubStatus } from '../services/articlepub-services/types'
import { type Mapping } from './type'

export const ArticlepubStatusMapping: Mapping<ArticlepubStatus> = {
  0: {
    key: 0,
    label: 'Draft',
    color: 'rgb(var(--color-txt-secondary))',
  },
  1: {
    key: 1,
    label: 'ToBePublish',
    color: 'rgb(var(--color-txt-positive-light))',
  },
  2: {
    key: 2,
    label: 'Publishing',
    color: 'rgb(var(--color-txt-danger))',
  },
  3: {
    key: 3,
    label: 'Published',
    color: 'rgb(var(--color-txt-positive-light))',
  }
} as const

export const ARTICLEPUB_STATUS_OPTIONS = [
  {
    label: ArticlepubStatusMapping[0].label,
    value: ArticlepubStatusMapping[0].key,
  },
  {
    label: ArticlepubStatusMapping[1].label,
    value: ArticlepubStatusMapping[1].key,
  },
  {
    label: ArticlepubStatusMapping[2].label,
    value: ArticlepubStatusMapping[2].key,
  },
]

export const REFINE_ARTICLEPUB_STATUS_OPTIONS = [
  {
    label: 'All',
    value: '',
  },
  ...ARTICLEPUB_STATUS_OPTIONS,
]