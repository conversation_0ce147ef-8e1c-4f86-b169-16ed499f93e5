import { network } from '@/libs/network'
import {
  type GetMessageListRequest,
  type GetMessageListResponse,
  type GetMessageDetailRequest,
  type GetMessageDetailResponse,
  type CreateMessageRequest,
  type UpdateMessageRequest,
  type DeleteMessageRequest,
  type PublishMessageRequest,
  CustomerList,
} from './types'

const URLs = {
  messageList: '/bo/crm/message/list',
  messageDetail: '/bo/crm/message/detail',
  createMessage: '/bo/crm/message/create',
  updateMessage: '/bo/crm/message/update',
  deleteMessage: '/bo/crm/message/delete',
  publishMessage: '/bo/crm/message/publish',
  createAndPublishMessage: '/bo/crm/message/createAndPublish',
  updateAndPublishMessage: '/bo/crm/message/updateAndPublish',
  getCustomerList: '/bo/customer-mgmt/rids',
}

export const CRMMessageCenterServices = {
  getMessageList: async (payload: GetMessageListRequest) => {
    console.log("查询条件：", payload)
    const { data } = await network.post<
      TableDataAPIResponse<GetMessageListResponse>
    >(URLs.messageList, payload)
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  getMessageDetail: async (payload: GetMessageDetailRequest) => {
    const { data } = await network.post<
      GeneralAPIResponse<GetMessageDetailResponse>
    >(URLs.messageDetail, payload)
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  createMessage: async (payload: CreateMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.createMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  updateMessage: async (payload: UpdateMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.updateMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  createAndPublishMessage: async (payload: CreateMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.createAndPublishMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  updateAndPublishMessage: async (payload: UpdateMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.updateAndPublishMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  deleteMessage: async (payload: DeleteMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.deleteMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  publishMessage: async (payload: PublishMessageRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.publishMessage,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  getCustomerList: async () => {
    const { data } = await network.post<GeneralAPIResponse<CustomerList>>(
      URLs.getCustomerList,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },
}