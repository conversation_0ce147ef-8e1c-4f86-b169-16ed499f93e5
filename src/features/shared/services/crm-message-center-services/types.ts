export type MessageStatus = 0 | 1 | 2 | 3 | 4 | 5 //-1: 已删除 0: 草稿, 1: 待发送, 2：发送中, 3: 已发送成功, 4: 部分失败, 5: 全部失败
export type MessageActionType = 'ADD' | 'EDIT' | 'DELETE' | 'PUBLISH'
export type ChannelType = 0

export interface GetMessageListRequest {
  searchValue: string
  searchType: string
  pageNo: number
  pageSize: number
  sortBy: string
  sortType: string
  startTime: number | null
  endTime: number | null
  status?: MessageStatus
}

export interface PublishChannel {
  channelType: ChannelType
  target: string // 接收人或设备
  userIds: string //接收人
}

export interface GetMessageListResponse {
  id: number
  title: string
  content: string
  contentSummary: string // 前30个字符
  status: MessageStatus
  publishChannels: PublishChannel[]
  createTime: number
  updateTime: number
  publishTime?: number
}

export interface GetMessageDetailRequest {
  id: number
}

export interface GetMessageDetailResponse {
  action: MessageActionType
  id?: number
  title: string
  content: string
  status: MessageStatus
  publishChannels: PublishChannel[]
  created?: number
  published?: number
}

export interface CreateMessageRequest {
  title: string
  content: string
  publishChannels: PublishChannel[]
  action: 'SAVE' | 'PUBLISH' // 暂存或保存并发布
}

export interface UpdateMessageRequest {
  id: number
  title: string
  content: string
  publishChannels: PublishChannel[]
  action: 'SAVE' | 'PUBLISH'
}

export interface DeleteMessageRequest {
  id: number
  action: MessageActionType
}

export interface PublishMessageRequest {
  messageId: number
  action: MessageActionType
}

//{
//     "customerList": [
//         {
//             "id": 2401,
//             "name": "accompany",
//             "code": "",
//             "rid": [
//                 "160a3797c93c2d07487"
//             ]
//         },
//         {
//             "id": 2501,
//             "name": "test3",
//             "code": "test03",
//             "rid": []
//         },
//         {
//             "id": 2601,
//             "name": "test04",
//             "code": "",
//             "rid": [
//                 "100d855908613ea693e"
//             ]
//         }
//     ]
// }
export interface CustomerObject {
  id: number
  name: string
  code: string
  rid: string[]
}

export interface CustomerList {
  customerList: CustomerObject[]
}