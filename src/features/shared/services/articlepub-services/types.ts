export type ArticlepubStatus = 0 | 1 | 2 | 3 //-1: 已删除 0: 草稿, 1: 待发布, 2：发布中, 3: 已发布
export type ArticlepubActionType = 'ADD' | 'EDIT' | 'DELETE' | 'PUBLISH'

export interface GetArticlepubListRequest {
  searchValue: string
  searchType: string
  pageNo: number
  pageSize: number
  sortBy: string
  sortType: string
  startTime: number | null
  endTime: number | null
  status?: ArticlepubStatus
}

export interface GetArticlepubListResponse {
  id: number
  title: string
  content: string
  contentSummary: string // 前30个字符
  status: ArticlepubStatus
  createTime: number
  updateTime: number
  publishTime?: number
}

export interface GetArticlepubDetailRequest {
  id: number
}

export interface GetArticlepubDetailResponse {
  action: ArticlepubActionType
  id?: number
  title: string
  content: string
  status: ArticlepubStatus
  created?: number
  published?: number
}

export interface CreateArticlepubRequest {
  title: string
  content: string
  action: 'SAVE' | 'PUBLISH' // 暂存或保存并发布
}

export interface UpdateArticlepubRequest {
  id: number
  title: string
  content: string
  action: 'SAVE' | 'PUBLISH'
}

export interface DeleteArticlepubRequest {
  id: number
  action: ArticlepubActionType
}

export interface PublishArticlepubRequest {
  articleId: number
  action: ArticlepubActionType
}