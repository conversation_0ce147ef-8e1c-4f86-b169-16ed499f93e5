import { network } from '@/libs/network'
import {
  type GetArticlepubListRequest,
  type GetArticlepubListResponse,
  type GetArticlepubDetailRequest,
  type GetArticlepubDetailResponse,
  type CreateArticlepubRequest,
  type UpdateArticlepubRequest,
  type DeleteArticlepubRequest,
  type PublishArticlepubRequest,
} from './types'

const URLs = {
  articlepubList: '/bo/crm/article/list',
  articlepubDetail: '/bo/crm/article/detail',
  createArticlepub: '/bo/crm/article/create',
  updateArticlepub: '/bo/crm/article/update',
  deleteArticlepub: '/bo/crm/article/delete',
  publishArticlepub: '/bo/crm/article/publish',
  createAndPublishArticlepub: '/bo/crm/article/createAndPublish',
  updateAndPublishArticlepub: '/bo/crm/article/updateAndPublish',
}

export const ArticlepubServices = {
  getArticlepubList: async (payload: GetArticlepubListRequest) => {
    const { data } = await network.post<
      TableDataAPIResponse<GetArticlepubListResponse>
    >(URLs.articlepubList, payload)
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  getArticlepubDetail: async (payload: GetArticlepubDetailRequest) => {
    const { data } = await network.post<
      GeneralAPIResponse<GetArticlepubDetailResponse>
    >(URLs.articlepubDetail, payload)
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  createArticlepub: async (payload: CreateArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.createArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  updateArticlepub: async (payload: UpdateArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.updateArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  createAndPublishArticlepub: async (payload: CreateArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.createAndPublishArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  updateAndPublishArticlepub: async (payload: UpdateArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.updateAndPublishArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  deleteArticlepub: async (payload: DeleteArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.deleteArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },

  publishArticlepub: async (payload: PublishArticlepubRequest) => {
    const { data } = await network.post<GeneralAPIResponse<unknown>>(
      URLs.publishArticlepub,
      payload,
    )
    if (data.code !== '0') {
      throw new Error(data.message)
    }
    return data.response
  },
}