import { useRecoilState } from 'recoil'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { articlepubDetailState } from '@/features/shared/states'
import { AppButton, AppModal } from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import { ArticlepubServices } from '@/features/shared/services'

export function ArticlepubPublishModal() {
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const [{ selectedArticlepub }, setArticlepubDetailState] = useRecoilState(
    articlepubDetailState,
  )

  const mutatePublishArticlepub = useMutation({
    mutationFn: ArticlepubServices.publishArticlepub,
    onError: (error) => {
      showToast('Article Published Failed', error.message, 'danger')
    },
    onSuccess: () => {
      __onClose()
      queryClient.invalidateQueries({
        queryKey: ['message-center-list'],
      })
      showToast('Article Published Successfully', 'success')
    },
  })

  const __onClose = () => {
    setArticlepubDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedArticlepub: undefined,
    }))
  }

  const __onConfirm = () => {
    if (selectedArticlepub?.id) {
      mutatePublishArticlepub.mutateAsync({
        articleId: selectedArticlepub.id,
        action: 'PUBLISH',
      })
    }
  }

  return (
    <AppModal
      onCancel={__onClose}
      open
      titleLabel="Publish Article"
      width={500}
    >
      <div className="flex flex-col gap-4">
        <p className="text-gray-700">
          Are you sure you want to publish the article "{selectedArticlepub?.title}"?
          This action cannot be undone.
        </p>

        <div className="flex justify-end gap-2">
          <AppButton
            cosmeticType="secondary"
            label="Cancel"
            onClick={__onClose}
            type="button"
          />
          <AppButton
            label="Publish"
            onClick={__onConfirm}
            type="button"
          />
        </div>
      </div>
    </AppModal>
  )
}