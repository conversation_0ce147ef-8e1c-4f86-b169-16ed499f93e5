import dayjs from 'dayjs'
import { useRecoilState } from 'recoil'
import { useQuery } from '@tanstack/react-query'
import { articlepubDetailState } from '@/features/shared/states'
import { AppButton, AppModal, AppNormalInput, AppNormalTextarea } from '@/features/shared/ui'
import { ArticlepubServices } from '@/features/shared/services'

export function ArticlepubViewModal() {
  const [{ selectedArticlepub }, setArticlepubDetailState] = useRecoilState(
    articlepubDetailState,
  )

  const { data: articlepubData } = useQuery({
    enabled: Boolean(selectedArticlepub?.id),
    queryFn: () =>
      ArticlepubServices.getArticlepubDetail({
        id: Number(selectedArticlepub?.id),
      }),
    queryKey: ['articlepub-detail-by-id', selectedArticlepub?.id],
  })

  const __onClose = () => {
    setArticlepubDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedArticlepub: undefined,
    }))
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0"
      onCancel={__onClose}
      open
      // titleDescription={messageData?.title}
      titleLabel="Message Details"
      width={600}
    >
      <div className="flex flex-col gap-3">
        <AppNormalInput
          label="Message Title"
          value={articlepubData?.title || ''}
          placeholder="-"
          readOnly
          disabled
        />

        <AppNormalTextarea
          label="Message Content"
          value={articlepubData?.content || ''}
          placeholder="-"
          readOnly
          disabled
          rows={6}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Publish Channels
          </label>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <AppNormalInput
            label="Created Time"
            value={articlepubData?.created
              ? dayjs(articlepubData.created * 1000).format('YYYY-MM-DD HH:mm:ss')
              : '-'
            }
            readOnly
            disabled
          />
          <AppNormalInput
            label="Published Time"
            value={articlepubData?.published
              ? dayjs(articlepubData.published * 1000).format('YYYY-MM-DD HH:mm:ss')
              : '-'
            }
            readOnly
            disabled
          />
        </div>

        <section className="sticky bottom-0 left-0 right-0 flex justify-end bg-white py-6">
          <AppButton
            cosmeticType="primary"
            label="Close"
            onClick={__onClose}
            type="button"
          />
        </section>
      </div>
    </AppModal>
  )
}