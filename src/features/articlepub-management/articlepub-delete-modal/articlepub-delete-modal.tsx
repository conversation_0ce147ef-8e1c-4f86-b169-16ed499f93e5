import { useRecoilState } from 'recoil'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { articlepubDetailState } from '@/features/shared/states'
import { AppButton, AppModal } from '@/features/shared/ui'
import { useToast } from '@/features/shared/utils'
import { ArticlepubServices } from '@/features/shared/services'

export function ArticlepubDeleteModal() {
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const [{ selectedArticlepub }, setArticlepubDetailState] = useRecoilState(
    articlepubDetailState,
  )

  const mutateDeleteArticlepub = useMutation({
    mutationFn: ArticlepubServices.deleteArticlepub,
    onError: (error) => {
      showToast('Articlepub Deletion Failed', error.message, 'danger')
    },
    onSuccess: () => {
      __onClose()
      queryClient.invalidateQueries({
        queryKey: ['articlepub-list'],
      })
      showToast('Articlepub Deleted Successfully', 'success')
    },
  })

  const __onClose = () => {
    setArticlepubDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedArticlepub: undefined,
    }))
  }

  const __onConfirm = () => {
    if (selectedArticlepub?.id) {
      mutateDeleteArticlepub.mutateAsync({
        id: selectedArticlepub.id,
        action: 'DELETE',
      })
    }
  }

  return (
    <AppModal
      onCancel={__onClose}
      open
      titleLabel="Delete Message"
      width={500}
    >
      <div className="flex flex-col gap-4">
        <p className="text-gray-700">
          Are you sure you want to delete the article "{selectedArticlepub?.title}"?
          This action cannot be undone.
        </p>

        <div className="flex justify-end gap-2">
          <AppButton
            cosmeticType="secondary"
            label="Cancel"
            onClick={__onClose}
            type="button"
          />
          <AppButton
            // cosmeticType="danger"
            label="Delete"
            // loading={mutateDeleteMessage.isPending}
            onClick={__onConfirm}
            type="button"
          />
        </div>
      </div>
    </AppModal>
  )
}