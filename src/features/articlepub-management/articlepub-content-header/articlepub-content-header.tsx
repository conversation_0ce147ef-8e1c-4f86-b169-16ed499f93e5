import { useRecoilState } from 'recoil'
import { AppButton } from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { articlepubDetailState } from '@/features/shared/states'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'

const CREATE_ARTICLEPUB_PERMISSION = 2300100

export function ArticlepubContentHeader() {
  const { hasPermission } = usePermission()
  const [, setArticlepubDetailState] = useRecoilState(articlepubDetailState)

  const openArticlepubModal = () => {
    setArticlepubDetailState((prev) => ({
      ...prev,
      mode: 'ADD',
      selectedArticlepub: undefined,
    }))
  }

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Article</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage and publish articles
        </p>
      </div>
      {hasPermission(CREATE_ARTICLEPUB_PERMISSION) && (
        <AppButton
            className="mr-2"
            label="Add"
            onClick={openArticlepubModal}
            prefixIcon={plusGreenIcon}
          />
      )}
    </div>
  )
}