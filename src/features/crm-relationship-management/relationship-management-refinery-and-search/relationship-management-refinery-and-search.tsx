import Select, { type BaseOptionType } from 'antd/es/select'
import { useSearchParams } from 'react-router-dom'
import { ReactSVG } from 'react-svg'
import { useState, useEffect } from 'react'
import { useDebounceValue } from 'usehooks-ts'
import { AppButton } from '@/features/shared/ui'
import { useRecoilValue } from 'recoil'
import { relationshipManagementDetailState } from '@/features/shared/states'
import { AppInputBeta } from '@/features/shared/ui/app-input-beta'
import {
  arrowClockwiseIcon,
  caretDownIcon,
  magnifyingGlassIcon,
  slidersHorizontalIcon,
} from '@/features/shared/ui/app-icons'

const RM_FILTER_OPTIONS: BaseOptionType[] = [
  {
    label: 'User ID',
    value: 'USER_ID',
  },
  {
    label: 'User Name',
    value: 'USER_NAME',
  },
]

const TEAM_FILTER_OPTIONS: BaseOptionType[] = [
  {
    label: 'Team ID',
    value: 'TEAM_ID',
  },
  {
    label: 'Team Name',
    value: 'TEAM_NAME',
  },
]
interface ResetRefineryButtonProps {
  resetKeyword: () => void
}
function ResetRefineryButton({ resetKeyword }: ResetRefineryButtonProps) {
  const [, setSearchParams] = useSearchParams()

  const resetRefinery = () => {
    setSearchParams(() => {
      return {}
    })
    resetKeyword()
  }

  return (
    <AppButton
      label="Reset"
      onClick={resetRefinery}
      prefixIcon={
        <ReactSVG
          beforeInjection={(svg) => {
            svg.classList.add('fill-txt-title')
          }}
          src={arrowClockwiseIcon}
        />
      }
    />
  )
}

interface CustomerRelationsRefineryAndSearchProps {
  onRefineListClick: () => void
}

export function RelationshipManagementRefineryAndSearch(
  props: CustomerRelationsRefineryAndSearchProps,
) {
  const { onRefineListClick } = props
  const [searchParams, setSearchParams] = useSearchParams()
  const keywordQuery = searchParams.get('keyword')
  const [keyword, setKeyword] = useState(keywordQuery || '')
  const [debouncedKeyword, setDebouncedKeyword] = useDebounceValue(
    keyword || '',
    300,
  )
  const searchTypeQuery = searchParams.get('searchType')
  const [searchType, setSearchType] = useState(searchTypeQuery)
  const adjustedSearchParamSize = searchParams.get('searchType')
    ? searchParams.size - 1
    : searchParams.size

  const { listType } = useRecoilValue(relationshipManagementDetailState)

  const resetKeyword = () => {
    setKeyword('')
  }

  useEffect(() => {
    setDebouncedKeyword(keyword)
  }, [keyword, setDebouncedKeyword])

  useEffect(() => {
    setSearchParams((prev) => {
      if (debouncedKeyword) {
        prev.set('keyword', debouncedKeyword)
      } else {
        prev.delete('keyword')
      }
      return prev
    })
  }, [debouncedKeyword, setSearchParams])

  useEffect(() => {
    let updatedSearchType = searchType

    if (
      listType === 'MANAGERS' &&
      !RM_FILTER_OPTIONS.some((option) => option.value === searchType)
    ) {
      updatedSearchType = RM_FILTER_OPTIONS[0]?.value || ''
    }
    if (
      listType === 'TEAMS' &&
      !TEAM_FILTER_OPTIONS.some((option) => option.value === searchType)
    ) {
      updatedSearchType = TEAM_FILTER_OPTIONS[0]?.value || ''
    }

    if (updatedSearchType !== searchType) {
      setKeyword('')
      setSearchType(updatedSearchType)
    }

    setSearchParams((prev) => {
      if (updatedSearchType) {
        prev.set('searchType', updatedSearchType)
      } else {
        prev.delete('searchType')
      }
      return prev
    })
  }, [listType, searchType, setSearchType, setSearchParams])

  return (
    <section className="flex h-[42px] justify-between">
      <div className="flex items-center bg-bg-accent py-1">
        <Select
          className="list-search-select"
          defaultValue={listType === 'MANAGERS' ? 'USER_ID' : 'TEAM_ID'}
          onChange={(value) => {
            setKeyword('')
            setTimeout(() => {
              setSearchType(value)
            }, 500)
          }}
          options={
            listType === 'MANAGERS' ? RM_FILTER_OPTIONS : TEAM_FILTER_OPTIONS
          }
          style={{ width: 133 }}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={caretDownIcon}
            />
          }
          value={searchType}
        />
        <div className="h-[22px] w-[1px] rounded bg-txt-inactive" />
        <AppInputBeta
          onChange={(e) => {
            setKeyword(e.target.value)
          }}
          placeholder="Search"
          prefixElement={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={magnifyingGlassIcon}
            />
          }
          value={keyword}
          wrapperClassName="w-[345px]"
        />
      </div>
      <div className="flex gap-2">
        <AppButton
          cosmeticType={adjustedSearchParamSize > 0 ? 'tertiary' : 'quaternary'}
          label="Refine"
          onClick={onRefineListClick}
          prefixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={slidersHorizontalIcon}
            />
          }
        />
        <ResetRefineryButton resetKeyword={resetKeyword} />
      </div>
    </section>
  )
}
