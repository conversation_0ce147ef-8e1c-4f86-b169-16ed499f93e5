import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  CRMRelationshipManagementServices,
  RM_MANAGER_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetRMManagerTableListResponse,
  type ManagerStatus,
  type PositionOption,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
} from '@/features/shared/ui/app-icons'
import { ManagersStatus, PositionOptions } from '@/features/shared/mappings'

const EDIT_RM_PERMISSION = 1700200
const DELETE_RM_PERMISSION = 1700300

interface RMManagerListTableProps {
  onRowClick: (
    record: GetRMManagerTableListResponse,
    action: 'EDIT' | 'DELETE',
  ) => void
}

export function RelationshipManagementManagersListTable(
  props: RMManagerListTableProps,
) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()
  const [searchParams] = useSearchParams()

  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)
  const updateFromTimeQuery = searchParams.get(SEARCH_PARAMS.updateFromTime)
  const updateToTimeQuery = searchParams.get(SEARCH_PARAMS.updateToTime)
  const userStatusQuery = searchParams.get(SEARCH_PARAMS.userStatus)
  const positionQuery = searchParams.get(SEARCH_PARAMS.position)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMManagerList({
        createFromTime: createFromTimeQuery
          ? dayjs(Number(createFromTimeQuery)).valueOf()
          : 0,
        createToTime: createToTimeQuery
          ? dayjs(Number(createToTimeQuery)).valueOf()
          : 0,
        keyword: keywordQuery || '',
        pageNum: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        position: positionQuery as PositionOption,
        searchType: keywordQuery ? searchTypeQuery || 'USER_ID' : '',
        sortBy: sortByQuery || 'CREATE_TIME',
        sortType: sortTypeQuery || 'DESC',
        updateFromTime: updateFromTimeQuery
          ? dayjs(Number(updateFromTimeQuery)).valueOf()
          : 0,
        updateToTime: updateToTimeQuery
          ? dayjs(Number(updateToTimeQuery)).valueOf()
          : 0,
        userStatus: userStatusQuery as ManagerStatus,
      }),
    queryKey: [
      'relationship-management-manager-list',
      keywordQuery,
      searchTypeQuery,
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
      createFromTimeQuery,
      createToTimeQuery,
      positionQuery,
      updateFromTimeQuery,
      updateToTimeQuery,
      userStatusQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const columns: TableProps<GetRMManagerTableListResponse>['columns'] = [
    {
      dataIndex: 'userId',
      key: 'RM_ID',
      sortOrder:
        sortByQuery === 'RM_ID'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'User ID',
    },
    {
      dataIndex: 'userName',
      key: 'userName',
      title: 'User Name',
    },
    {
      dataIndex: 'userStatus',
      key: 'RM_STATUS',
      render: (status: string) =>
        status ? (
          <span
            className="font-light"
            style={{
              color: ManagersStatus[status as ManagerStatus].color ?? '#000',
            }}
          >
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              ManagersStatus[status as ManagerStatus]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      sortOrder:
        sortByQuery === 'RM_STATUS'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Status',
    },
    {
      dataIndex: 'teamName',
      key: 'teamName',
      title: 'Team',
    },
    {
      dataIndex: 'position',
      key: 'position',
      render: (position: string) =>
        position ? (
          <span className="font-light">
            {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
              PositionOptions[position as PositionOption]?.label ?? '-'
            }
          </span>
        ) : (
          '-'
        ),
      title: 'Position',
    },
    {
      dataIndex: 'email',
      key: 'email',
      title: 'Email Address',
    },
    {
      dataIndex: 'createTime',
      key: 'CREATE_TIME',
      render: (createTime: string) => (
        <span>
          {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'CREATE_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Created Date',
    },
    {
      dataIndex: 'id',
      key: 'id',
      render: (id: number, record) => (
        <div>
          {hasPermission(EDIT_RM_PERMISSION) ||
          hasPermission(DELETE_RM_PERMISSION) ? (
            <Popover
              arrow={false}
              content={
                <div className="flex flex-col">
                  {hasPermission(EDIT_RM_PERMISSION) && (
                    <AppButton
                      className="justify-start py-2 font-normal"
                      cosmeticType="transparent"
                      label="Edit/View RM"
                      onClick={(e) => {
                        e.stopPropagation()
                        onRowClick(record, 'EDIT')
                        setSelectedPopover(null)
                      }}
                      prefixIcon={editIcon}
                      type="button"
                    />
                  )}

                  {hasPermission(DELETE_RM_PERMISSION) && (
                    <AppButton
                      className="justify-start py-2 font-normal"
                      cosmeticType="transparent"
                      label="Delete RM"
                      onClick={(e) => {
                        e.stopPropagation()
                        onRowClick(record, 'DELETE')
                        setSelectedPopover(null)
                      }}
                      prefixIcon={trashIcon}
                      type="button"
                    />
                  )}
                </div>
              }
              onOpenChange={(newOpen) => {
                handleOpenChange(newOpen, id)
              }}
              open={selectedPopover === id}
              placement="bottomRight"
              trigger="click"
            >
              <AppButton
                cosmeticType="transparent"
                onClick={(e) => {
                  e.stopPropagation()
                }}
                prefixIcon={
                  <ReactSVG
                    beforeInjection={(svg) => {
                      svg.classList.add('fill-txt-title')
                    }}
                    src={dotsThreeVerticalIcon}
                  />
                }
                type="button"
              />
            </Popover>
          ) : null}
        </div>
      ),
      title: ' ',
      width: 40,
    },
  ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="leadId"
      total={data?.total}
    />
  )
}
