import { useEffect, useState } from 'react'
import { useRecoilState } from 'recoil'
import { useForm } from 'react-hook-form'
import { ReactSVG } from 'react-svg'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { relationshipManagementDetailState } from '@/features/shared/states'
import {
  AppButton,
  AppModal,
  AppNormalInput,
  ControlledAppSelectInput,
  RequiredIndicator,
  AppInfoCardVariant9,
  MeritDivider,
  AppSwitchButton,
} from '@/features/shared/ui'
import { AppEditButton } from '@/features/shared/ui/app-buttons/app-edit-button/app-edit-button'
import { usePermission, useToast } from '@/features/shared/utils'
import {
  CRMRelationshipManagementServices,
  type RMDetailActionType,
  type TeamListObject,
  type ManagerList,
} from '@/features/shared/services'
import { plusIcon, trashFilledIcon } from '@/features/shared/ui/app-icons'
import { TEAM_MODAL_TYPE_OPTIONS } from '@/features/shared/mappings'
import { GroupList } from './ui/group-list-components'
import { AssignedCustomerList } from './ui/assign-customer-components'

const ADD_PERMISSION = 1700600
const EDIT_PERMISSION = 1700700

const schema = yup
  .object()
  .shape({
    customerList: yup.array(),
    teamHeadId: yup.number().required('Team Head is a required field'),
    teamId: yup.number(),
    teamManagerId: yup.number().nullable(),
    teamMemberId: yup.number(),
    teamName: yup.string().required('Team Name is a required field'),
  })
  .required()

type FormValues = yup.InferType<typeof schema>

export function RelationshipManagementTeamAddEditModal() {
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const { hasPermission } = usePermission()
  const [
    { mode, selectedTeam, isSaveDisabled },
    setRelationshipManagementDetailState,
  ] = useRecoilState(relationshipManagementDetailState)

  const [teamGroupList, setTeamGroupList] = useState<ManagerList[]>([])
  const [selectedTeamMembers, setSelectedTeamMembers] = useState<number[]>([])
  const [editGroupIndex, setEditGroupIndex] = useState<number | undefined>(
    undefined,
  )
  const [tabValue, setTabValue] = useState<string>('INFO')

  const { data: teamHeadData } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMMemberList({
        isCustomer: 'FALSE',
        isRm: 'TRUE',
        memberType: 'HEAD',
        pageNum: 1,
        pageSize: 99999,
      }),
    queryKey: ['teamHeadData'],
  })
  const { data: teamManagerData } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMMemberList({
        isCustomer: 'FALSE',
        isRm: 'TRUE',
        memberType: 'MANAGER',
        pageNum: 1,
        pageSize: 99999,
      }),
    queryKey: ['teamManagerData'],
  })
  const { data: teamMemberData } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMMemberList({
        isCustomer: 'FALSE',
        isRm: 'TRUE',
        memberType: 'MEMBER',
        pageNum: 1,
        pageSize: 99999,
      }),
    queryKey: ['teamMemberData'],
  })
  const { data: teamData } = useQuery({
    enabled: Boolean(selectedTeam?.teamId),
    queryFn: () =>
      CRMRelationshipManagementServices.getRMTeamDetail({
        teamId: Number(selectedTeam?.teamId),
      }),
    queryKey: ['rm-team-detail-by-id', selectedTeam?.teamId],
  })

  const {
    formState: { isValid, errors },
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      customerList: [],
      teamHeadId: teamData?.teamHead.userId,
      teamId: teamData?.teamId ?? undefined,
      teamManagerId: undefined,
      teamMemberId: undefined,
      teamName: teamData?.teamName,
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })

  useEffect(() => {
    if (teamData) {
      reset({
        teamHeadId: teamData.teamHead.userId,
        teamId: teamData.teamId,
        teamManagerId: undefined,
        teamMemberId: undefined,
        teamName: teamData.teamName,
      })
      setTeamGroupList(teamData.managerList)
    }
    if (mode === 'ASSIGN') {
      setTabValue('CUSTOMER')
    }
  }, [teamData, reset])

  const watchedManagerId = watch('teamManagerId')
  const watchedTeamName = watch('teamName')

  const mutateEditRMTeamDetail = useMutation({
    mutationFn: CRMRelationshipManagementServices.editRMTeamDetail,
    onError: (error) => {
      showToast('Team Details Update Failed', error.message, 'danger')
    },
    onSuccess: () => {
      __onClose()
      queryClient.invalidateQueries({
        queryKey: ['relationship-management-team-list'],
      })
      mode === 'ADD'
        ? showToast('A New Team Created Successfully', 'success')
        : showToast(
            'Team Details Updated Successfully',
            `You have updated a team - ${watchedTeamName}`,
            'success',
          )
    },
  })

  const __onClose = () => {
    reset({})
    setRelationshipManagementDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedManager: undefined,
    }))
  }

  const __onSubmit = handleSubmit((data: FormValues) => {
    if (isValid) {
      const payload = {
        action:
          mode === 'ADD'
            ? ('ADD' as RMDetailActionType)
            : ('EDIT' as RMDetailActionType),
        managerList: teamGroupList,
        teamHead: {
          userId: data.teamHeadId,
        },
        teamId: data.teamId!,
        teamName: data.teamName,
      }
      mutateEditRMTeamDetail.mutateAsync(payload)
    }
  })

  const resetManagerAndMembers = () => {
    reset({
      ...getValues(),
      teamManagerId: undefined,
    })
    setSelectedTeamMembers([])
  }

  const addGroup = () => {
    const list: TeamListObject[] = []
    if (selectedTeamMembers.length > 0) {
      selectedTeamMembers.forEach((item: number) => {
        list.push({
          userId: item,
          userName:
            teamMemberData?.list?.find((member) => member.userId === item)
              ?.userName || '',
        })
      })
    }
    if (editGroupIndex !== undefined) {
      teamGroupList.splice(editGroupIndex, 1, {
        memberList: list,
        userId: watchedManagerId || undefined,
        userName:
          teamManagerData?.list?.find(
            (manager) => manager.userId === watchedManagerId,
          )?.userName || '',
      })
      setEditGroupIndex(undefined)
    } else {
      setTeamGroupList([
        ...teamGroupList,
        {
          memberList: list,
          userId: watchedManagerId || undefined,
          userName:
            teamManagerData?.list?.find(
              (manager) => manager.userId === watchedManagerId,
            )?.userName || '',
        },
      ])
    }

    resetManagerAndMembers()
  }

  const deleteMember = (index: number) => {
    setSelectedTeamMembers((prevMembers) =>
      prevMembers.filter((_, i) => i !== index),
    )
  }

  const openEditGroup = (index: number) => {
    setEditGroupIndex(index)
    const data = teamGroupList[index]!
    setValue('teamManagerId', data.userId)
    const memberList = data.memberList?.map((item) => item.userId)
    setSelectedTeamMembers(memberList as number[])
  }

  const cancelEditGroup = () => {
    setEditGroupIndex(undefined)
    resetManagerAndMembers()
  }

  const deleteGroup = (index: number) => {
    setTeamGroupList((prevGroups) => prevGroups.filter((_, i) => i !== index))
  }

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleLabel={mode === 'ADD' ? 'Create Team' : 'View/Edit Team'}
      width={600}
    >
      <AppInfoCardVariant9
        containerClassName="mb-3"
        contents={[{ label: 'Team ID', value: selectedTeam?.teamId }]}
      />
      {mode && mode !== 'ADD' ? (
        <div className="mb-3 grow">
          <section className="grid grid-cols-2 bg-bg-tone">
            {TEAM_MODAL_TYPE_OPTIONS.map((tab) => (
              <AppSwitchButton
                className="h-10"
                isActive={tab.value === tabValue}
                key={tab.value}
                onClick={() => {
                  setTabValue(tab.value)
                }}
              >
                {tab.label}
              </AppSwitchButton>
            ))}
          </section>
        </div>
      ) : null}
      <form className="flex flex-col gap-3" onSubmit={__onSubmit}>
        {tabValue === 'INFO' ? (
          <>
            <div className="grid grid-cols-2 gap-2">
              <AppNormalInput
                label="Team Name"
                {...register('teamName')}
                error={errors.teamName?.message}
                inputClassName="bg-bg-tone"
                placeholder="Input Team Name"
                required
              />
              <ControlledAppSelectInput
                control={control}
                label="Team Head"
                name="teamHeadId"
                optionFilterProp="label"
                options={teamHeadData?.list?.map((item) => ({
                  label: item.userName,
                  value: item.userId,
                }))}
                placeholder="Select Team Head"
                required
                showSearch
              />
            </div>
            <MeritDivider />
            {teamGroupList.length > 0
              ? teamGroupList.map((item, index) => (
                  <GroupList
                    deleteGroup={deleteGroup}
                    index={index}
                    item={item}
                    key={index}
                    openEditGroup={openEditGroup}
                  />
                ))
              : null}
            <div className="grid grid-cols-2 gap-2">
              <ControlledAppSelectInput
                control={control}
                label="Team Manager"
                name="teamManagerId"
                optionFilterProp="label"
                options={teamManagerData?.list
                  ?.filter((item) => {
                    // filter exist user in group list
                    return !teamGroupList.some(
                      (team) => team.userId === item.userId,
                    )
                  })
                  .map((item) => ({
                    label: item.userName,
                    value: item.userId,
                  }))}
                placeholder="Select Team Manager"
                required
                showSearch
              />
              <ControlledAppSelectInput
                control={control}
                label="Team Members"
                name="teamMemberId"
                onChange={(value) => {
                  setSelectedTeamMembers([...selectedTeamMembers, value])
                  setTimeout(() => {
                    setValue('teamMemberId', undefined)
                  }, 0)
                }}
                optionFilterProp="label"
                options={teamMemberData?.list
                  ?.filter((item) => {
                    // get existing members from teamGroupList
                    const existingUserIds = teamGroupList.flatMap((group) =>
                      group.memberList?.map((member) => member.userId),
                    )
                    // filter selected member & teamGroupList's existing members
                    return (
                      !existingUserIds.includes(item.userId) &&
                      !selectedTeamMembers.includes(item.userId)
                    )
                  })
                  .map((item) => ({
                    label: item.userName,
                    value: item.userId,
                  }))}
                placeholder="Select Team Members"
                showSearch
              />
            </div>
            {selectedTeamMembers.length > 0 ? (
              <div>
                <div className="mb-3 text-[10px] font-medium text-txt-inactive">
                  Selected Team Members
                </div>
                {selectedTeamMembers.map((id, index) => (
                  <div
                    className="flex flex-row flex-wrap items-center justify-between rounded-sm bg-bg-pale p-3"
                    key={index}
                    style={{ marginTop: index === 0 ? '' : '4px' }}
                  >
                    <div className="text-sm font-medium not-italic text-txt-title">
                      {id} -{' '}
                      {teamMemberData?.list?.find(
                        (member) => member.userId === id,
                      )?.userName || ''}
                    </div>
                    <AppEditButton
                      icon={trashFilledIcon}
                      label=" "
                      onClick={() => {
                        deleteMember(index)
                      }}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div>
                <div className="mb-3 text-[10px] font-medium text-txt-inactive">
                  Selected Team Members
                </div>
                <div className="flex items-center justify-center p-4 text-sm font-light text-txt-inactive">
                  – No Team Members Added –
                </div>
              </div>
            )}
            <RequiredIndicator />
            {editGroupIndex !== undefined ? (
              <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white">
                <AppButton
                  className="w-full"
                  cosmeticType="skeleton"
                  label="Cancel Edit"
                  onClick={() => {
                    cancelEditGroup()
                  }}
                />
                <AppButton
                  className="w-full"
                  cosmeticType="secondary"
                  label="Save"
                  onClick={() => {
                    addGroup()
                  }}
                />
              </section>
            ) : (
              <AppButton
                className="w-full"
                cosmeticType="secondary"
                disabled={!watchedManagerId}
                label="Add Group"
                onClick={addGroup}
                prefixIcon={
                  <ReactSVG
                    beforeInjection={(svg) => {
                      svg.classList.add(
                        watchedManagerId
                          ? 'fill-txt-inverted'
                          : 'fill-txt-title',
                      )
                    }}
                    src={plusIcon}
                  />
                }
              />
            )}
          </>
        ) : (
          <AssignedCustomerList customerList={teamData?.customerList || []} />
        )}
        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white pb-6 pt-3">
          <AppButton
            cosmeticType="skeleton"
            label="Cancel"
            onClick={__onClose}
          />
          {mode === 'ADD' && hasPermission(ADD_PERMISSION) && (
            <AppButton
              className="grow"
              cosmeticType="secondary"
              disabled={
                !isValid ||
                teamGroupList.length === 0 ||
                editGroupIndex !== undefined
              }
              isLoading={mutateEditRMTeamDetail.isPending}
              label="Create Team"
              type="submit"
            />
          )}
          {mode !== 'ADD' && hasPermission(EDIT_PERMISSION) && (
            <AppButton
              cosmeticType="secondary"
              disabled={
                !isValid ||
                (isSaveDisabled === false && tabValue === 'CUSTOMER') ||
                teamGroupList.length === 0 ||
                editGroupIndex !== undefined
              }
              isLoading={mutateEditRMTeamDetail.isPending}
              label="Update Team"
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
