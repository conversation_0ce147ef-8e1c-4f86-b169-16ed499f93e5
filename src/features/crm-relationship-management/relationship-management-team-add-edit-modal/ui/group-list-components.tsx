import { memberIcon, trashIcon } from '@/features/shared/ui/app-icons'
import numeral from 'numeral'
import {
  type TeamListObject,
  type ManagerList,
} from '@/features/shared/services'
import { ReactSVG } from 'react-svg'
import { AppEditButton } from '@/features/shared/ui/app-buttons/app-edit-button/app-edit-button'

interface GroupIndexProps {
  index: number
}
function GroupIndex(props: GroupIndexProps) {
  const { index } = props
  return (
    <div className="rounded-sm bg-btn-primary px-2 py-0.5">
      <span className="text-center text-xs font-medium capitalize not-italic text-txt-inverted">
        Group {numeral(index).format('00')}
      </span>
    </div>
  )
}
interface BriefInformationChunkProps {
  label: string
  value?: string
  isArray?: boolean
  arrayValue?: TeamListObject[]
}
function BriefInformationChunk(props: BriefInformationChunkProps) {
  const { label, value, isArray, arrayValue } = props
  return (
    <div className="flex items-center gap-1">
      <span className="text-xs font-medium capitalize not-italic leading-[14px] text-txt-title">
        {label}:
      </span>
      {isArray && Array.isArray(arrayValue) && arrayValue.length > 0 ? (
        arrayValue.map((item, index) => (
          <span
            key={index}
            className="flex items-center gap-1 rounded-sm bg-bg-default px-1 py-0.5 text-[10px] font-light not-italic leading-[14px] text-txt-paragraph"
          >
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={memberIcon}
            />
            {item.userName}
          </span>
        ))
      ) : (
        <span className="flex items-center gap-1 rounded-sm bg-bg-default px-1 py-0.5 text-[10px] font-light not-italic leading-[14px] text-txt-paragraph">
          <ReactSVG
            beforeInjection={(svg) => {
              svg.classList.add('fill-txt-title')
            }}
            src={memberIcon}
          />
          {value}
        </span>
      )}
    </div>
  )
}

interface GroupListProps {
  item: ManagerList
  index: number
  openEditGroup: (index: number) => void
  deleteGroup: (index: number) => void
}

export function GroupList(props: GroupListProps) {
  const { item, index, openEditGroup, deleteGroup } = props
  return (
    <div className="grid grid-cols-[1fr,auto] gap-2 bg-bg-tone p-3">
      <div>
        <div className="mb-1 flex flex-row flex-wrap items-center gap-x-3 gap-y-1">
          <GroupIndex index={index + 1} />
          <BriefInformationChunk label="Team Manager" value={item.userName} />
        </div>
        <BriefInformationChunk
          label="Team Members"
          isArray
          arrayValue={item.memberList}
        />
      </div>
      <div className="flex flex-row flex-wrap items-center gap-x-1 gap-y-1">
        <AppEditButton
          label=" "
          onClick={() => {
            openEditGroup(index)
          }}
        />
        <AppEditButton
          label=" "
          icon={trashIcon}
          onClick={() => {
            deleteGroup(index)
          }}
        />
      </div>
    </div>
  )
}
