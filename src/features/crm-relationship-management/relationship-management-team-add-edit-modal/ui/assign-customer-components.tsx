import { AutoComplete, Input } from 'antd'
import { ReactSVG } from 'react-svg'
import { useState, useEffect } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useRecoilState } from 'recoil'
import { relationshipManagementDetailState } from '@/features/shared/states'
import { AppButton } from '@/features/shared/ui'
import { AppEditButton } from '@/features/shared/ui/app-buttons/app-edit-button/app-edit-button'
import {
  CRMRelationshipManagementServices,
  type TeamListObject,
  type RiskLevel,
} from '@/features/shared/services'
import { useToast } from '@/features/shared/utils'
import {
  magnifyingGlassIcon,
  xCircleDarkIcon,
  trashFilledIcon,
} from '@/features/shared/ui/app-icons'

interface AssignCustomerListProps {
  customerList: TeamListObject[]
}

export function AssignedCustomerList(props: AssignCustomerListProps) {
  const { customerList } = props
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const [{ selectedTeam }, setRelationshipManagementDetailState] =
    useRecoilState(relationshipManagementDetailState)

  const { data: assignedCustomerData } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMMemberList({
        isRm: 'FALSE',
        isCustomer: 'TRUE',
        pageNum: 1,
        pageSize: 99999,
      }),
    queryKey: ['assignedCustomerData'],
  })

  const [autoCompleteValue, setAutoCompleteValue] = useState('')
  const [selectedAssignCustomers, setSelectedAssignCustomers] = useState<
    number[]
  >([])
  const [initialCustomerList, setInitialCustomerList] = useState<number[]>([])

  useEffect(() => {
    if (customerList.length > 0) {
      const list = customerList.map((item) => item.userId)
      setSelectedAssignCustomers(list as number[])
      setInitialCustomerList(list as number[])
    }
  }, [customerList])

  const options =
    assignedCustomerData?.list
      ?.filter(
        (item) => !selectedAssignCustomers.some((id) => id === item.userId),
      )
      .map((item) => ({
        label: item.userName,
        value: item.userId,
      })) || []

  const submitChanges = () => {
    const list: TeamListObject[] = []
    if (selectedAssignCustomers.length > 0) {
      selectedAssignCustomers.forEach((item: number) => {
        list.push({
          userId: item,
          userName:
            assignedCustomerData?.list?.find(
              (customer) => customer.userId === item,
            )?.userName || '',
        })
      })
    }
    const payload = {
      teamId: selectedTeam?.teamId || 0,
      customerList: list,
    }
    mutateAssignRMTeamCustomer.mutateAsync(payload)
  }

  const mutateAssignRMTeamCustomer = useMutation({
    mutationFn: CRMRelationshipManagementServices.assignRMTeamCustomer,
    onError: (error) => {
      showToast('Customers Assigned updated Failed', error.message, 'danger')
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['rm-team-detail-by-id', selectedTeam?.teamId],
      })

      showToast(
        'Customers Assigned updated Successfully',
        // `You have updated a team - ${watchedTeamName}`,
        'success',
      )
    },
  })

  const deleteCustomer = (index: number) => {
    setSelectedAssignCustomers((prevCustomers) =>
      prevCustomers.filter((_, i) => i !== index),
    )
  }

  const getRiskLevel = (id: number) => {
    return (
      assignedCustomerData?.list?.find((customer) => customer.userId === id)
        ?.riskLevel || ''
    )
  }

  const riskLevelClass = {
    LOW: 'bg-bg-accent text-txt-label',
    MEDIUM: 'bg-bg-caution text-txt-caution',
    HIGH: 'bg-bg-negative text-txt-negative',
  }
  const isSaveDisabled =
    JSON.stringify(selectedAssignCustomers) ===
    JSON.stringify(initialCustomerList)

  useEffect(() => {
    setRelationshipManagementDetailState((prev) => ({
      ...prev,
      isSaveDisabled,
    }))
  }, [isSaveDisabled])

  return (
    <div className="flex flex-col gap-3">
      <div className="flex items-center justify-between rounded-sm bg-bg-tone">
        <AutoComplete
          onSearch={(value) => {
            setAutoCompleteValue(value)
          }}
          onSelect={(value) => {
            setAutoCompleteValue('')
            setSelectedAssignCustomers([
              ...selectedAssignCustomers,
              Number(value),
            ])
          }}
          options={options}
          optionRender={(option) => `${option.value} - ${option.label}`}
          filterOption={(input, option) =>
            `${option?.value} - ${option?.label}`
              .toLowerCase()
              .includes(input.toLowerCase())
          }
          style={{ height: 'auto', width: '100%' }}
          value={autoCompleteValue}
        >
          <Input
            className="whitespace-no-wrap h-11 w-full overflow-hidden rounded-sm border-none bg-bg-tone px-3 py-2.5 text-xs font-light leading-[14px] shadow-sm ring-inset placeholder:text-txt-inactive hover:bg-bg-tone focus:bg-bg-tone focus:ring-0"
            placeholder="Search customer ..."
            prefix={
              <ReactSVG
                beforeInjection={(svg) => {
                  svg.classList.add('fill-txt-title')
                }}
                src={magnifyingGlassIcon}
              />
            }
            allowClear={{
              clearIcon: (
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('fill-txt-title', 'cursor-pointer')
                  }}
                  src={xCircleDarkIcon}
                />
              ),
            }}
          />
        </AutoComplete>
      </div>
      {selectedAssignCustomers.length > 0 ? (
        <div>
          <div className="mb-3 text-[10px] font-medium text-txt-inactive">
            Selected Customers
          </div>
          {selectedAssignCustomers.map((id, index) => (
            <div
              key={index}
              className="flex flex-row flex-wrap items-center justify-between rounded-sm bg-bg-pale p-3"
              style={{ marginTop: index === 0 ? '' : '4px' }}
            >
              <div>
                <div className="text-sm font-medium not-italic text-txt-title">
                  {assignedCustomerData?.list?.find(
                    (customer) => customer.userId === id,
                  )?.userName || ''}{' '}
                  /
                </div>
                <div className="text-xs not-italic text-txt-paragraph">
                  {id}
                </div>
              </div>
              <div className="flex flex-row flex-wrap items-center gap-1">
                <span
                  className={`rounded-sm px-2 py-0.5 text-xs font-medium not-italic ${riskLevelClass[getRiskLevel(id) as RiskLevel]}`}
                >
                  {getRiskLevel(id)}
                </span>
                <AppEditButton
                  label=" "
                  icon={trashFilledIcon}
                  onClick={() => {
                    deleteCustomer(index)
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div>
          <div className="mb-3 text-[10px] font-medium text-txt-inactive">
            Selected Customers
          </div>
          <div className="flex items-center justify-center p-4 text-sm font-light text-txt-inactive">
            – No Selected Customers Added –
          </div>
        </div>
      )}
      <section className="sticky bottom-0 left-0 right-0 grid grid-cols-1 gap-2 bg-white pt-3">
        <AppButton
          className="grow"
          cosmeticType="secondary"
          label="Save"
          disabled={isSaveDisabled}
          isLoading={mutateAssignRMTeamCustomer.isPending}
          onClick={() => {
            submitChanges()
          }}
        />
      </section>
    </div>
  )
}
