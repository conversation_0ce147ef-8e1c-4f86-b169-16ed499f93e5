import { useEffect } from 'react'
import { useRecoilState } from 'recoil'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { relationshipManagementDetailState } from '@/features/shared/states'
import {
  AppButton,
  AppModal,
  ControlledAppChoiceInput,
  ControlledAppSelectInput,
  RequiredIndicator,
  AppInfoCardVariant9,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import {
  MANAGER_STATUS_OPTIONS,
  POSOTION_OPTIONS,
} from '@/features/shared/mappings'
import {
  CRMRelationshipManagementServices,
  type RMDetailActionType,
  type PositionOption,
} from '@/features/shared/services'

const ADD_PERMISSION = 1700100
const EDIT_PERMISSION = 1700200

const schema = yup
  .object()
  .shape({
    email: yup.string(),
    position: yup.string().required('Position is a required field'),
    teamName: yup.string(),
    userId: yup.number().required('RM is a required field'),
    userName: yup.string(),
    userStatus: yup.string().required('Status is a required field'),
  })
  .required()

type FormValues = yup.InferType<typeof schema>

export function RelationshipManagementManagerAddEditModal() {
  const queryClient = useQueryClient()
  const { showToast } = useToast()
  const { hasPermission } = usePermission()
  const [{ mode, selectedManager }, setRelationshipManagementDetailState] =
    useRecoilState(relationshipManagementDetailState)

  const { data: rmData } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMMemberList({
        isCustomer: 'FALSE',
        isRm: 'FALSE',
        pageNum: 1,
        pageSize: 99999,
      }),
    queryKey: ['rmData'],
  })

  const { data: managerData } = useQuery({
    enabled: Boolean(selectedManager?.id),
    queryFn: () =>
      CRMRelationshipManagementServices.getRMManagerDetail({
        id: Number(selectedManager?.id),
      }),
    queryKey: ['rm-manager-detail-by-id', selectedManager?.id],
  })

  const {
    formState: { isValid },
    // formState: { isValid, errors },
    // register,
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      email: managerData?.email ?? '',
      position: managerData?.position ?? '',
      teamName: managerData?.teamName ?? '',
      userId: managerData?.userId ?? undefined,
      userName: managerData?.userName ?? '',
      userStatus: managerData?.userStatus ?? 'ACTIVE',
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })

  const watchedUserId = watch('userId')
  const watchedEmail = watch('email')

  useEffect(() => {
    if (managerData) {
      reset({
        email: managerData.email,
        position: managerData.position,
        teamName: managerData.teamName,
        userId: managerData.userId || undefined,
        userName: managerData.userName,
        userStatus: managerData.userStatus,
      })
    }
  }, [managerData, reset])

  const mutateEditRMManagerDetail = useMutation({
    mutationFn: CRMRelationshipManagementServices.editRMManagerDetail,
    onError: (error) => {
      showToast('Lead Details Update Unsuccessful', error.message, 'danger')
    },
    onSuccess: () => {
      __onClose()
      queryClient.invalidateQueries({
        queryKey: ['relationship-management-manager-list'],
      })
      mode === 'ADD'
        ? showToast('A New Lead Created Successfully', 'success')
        : showToast(
            'Lead Details Updated Successfully',
            // `You have updated a lead - ${watchedName}`,
            'success',
          )
    },
  })

  const __onClose = () => {
    reset({})
    setRelationshipManagementDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedManager: undefined,
    }))
  }

  const __onSubmit = handleSubmit((data: FormValues) => {
    if (isValid) {
      const payload = {
        action: mode! as RMDetailActionType,
        email: data.email || '',
        id: Number(selectedManager?.id),
        position: data.position as PositionOption,
        teamName: data.teamName || '',
        userId: Number(data.userId),
        userName:
          rmData?.list?.find((rm) => rm.userId === data.userId)?.userName || '',
        userStatus: data.userStatus,
      }
      mutateEditRMManagerDetail.mutateAsync(payload)
    }
  })

  return (
    <AppModal
      childrenWrapperClassName="pb-0" // required for sticky footer
      onCancel={__onClose}
      open
      titleLabel={mode === 'ADD' ? 'Add RM' : 'View/Edit RM'}
      width={600}
    >
      <form className="flex flex-col gap-3" onSubmit={__onSubmit}>
        <AppInfoCardVariant9
          containerClassName="mb-1"
          contents={[
            {
              label: 'User ID',
              value: selectedManager?.userId || watchedUserId,
            },
            {
              label: 'Email Address',
              value: selectedManager?.email || watchedEmail,
            },
            { label: 'Team', value: selectedManager?.teamName },
          ]}
        />
        <div className="grid grid-cols-2 gap-2">
          <ControlledAppSelectInput
            control={control}
            label="RM"
            name="userId"
            onChange={(value) => {
              setValue(
                'email',
                rmData?.list?.find((rm) => rm.userId === value)?.email,
              )
            }}
            optionFilterProp="label"
            options={rmData?.list?.map((item) => ({
              label: item.userName,
              value: item.userId,
            }))}
            placeholder="Select RM"
            required
            showSearch
          />
        </div>
        <div className="grid grid-cols-1 gap-2">
          <ControlledAppChoiceInput
            cols={2}
            control={control}
            label="Status"
            name="userStatus"
            options={MANAGER_STATUS_OPTIONS.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
            required
          />
        </div>
        <div className="grid grid-cols-2 gap-2">
          <ControlledAppSelectInput
            control={control}
            label="Position"
            name="position"
            optionFilterProp="label"
            options={POSOTION_OPTIONS}
            placeholder="Select position"
            required
            showSearch
          />
        </div>

        <RequiredIndicator />

        <section className="sticky bottom-0 left-0 right-0 grid grid-cols-2 gap-2 bg-white py-6">
          <AppButton
            cosmeticType="skeleton"
            label="Cancel"
            onClick={__onClose}
          />

          {mode === 'ADD' && hasPermission(ADD_PERMISSION) && (
            <AppButton
              className="grow"
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditRMManagerDetail.isPending}
              label="Add User"
              type="submit"
            />
          )}

          {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
            <AppButton
              cosmeticType="secondary"
              disabled={!isValid}
              isLoading={mutateEditRMManagerDetail.isPending}
              label="Save Changes"
              type="submit"
            />
          )}
        </section>
      </form>
    </AppModal>
  )
}
