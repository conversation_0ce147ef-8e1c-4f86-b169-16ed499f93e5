import { DatePicker } from 'antd'
import { useRef, useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { ReactSVG } from 'react-svg'
import type { PickerRef } from 'rc-picker'
import { clsx } from 'clsx'
import dayjs, { type Dayjs } from 'dayjs'
import { useSearchParams } from 'react-router-dom'
import { useRecoilValue } from 'recoil'
import { AppButton, AppRefineryDrawer } from '@/features/shared/ui'
import {
  arrowHorizontalLeftIcon,
  arrowHorizontalRightIcon,
  arrowsHorizontalIcon,
  calendarBlankIcon,
} from '@/features/shared/ui/app-icons'
import { parseTimeQueryParam } from '@/features/shared/utils'
import { RM_MANAGER_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import {
  REFINE_MANAGER_STATUS_OPTIONS,
  REFINE_POSOTION_OPTIONS,
} from '@/features/shared/mappings'
import { relationshipManagementDetailState } from '@/features/shared/states'

const schema = yup
  .object()
  .shape({
    countryRegion: yup.string(),
  })
  .required()

export type FormValues = yup.InferType<typeof schema>

interface RMListRefineryDrawerProps {
  isOpened: boolean
  onClose: () => void
}

interface RefineSectionProps {
  title: string
  titleIcon?: string
  refineList: {
    label: string
    labelColor?: string
    value: string
    suffixIcon?: string
    indicatorColor?: string
    prefixIcon?: string
  }[]
  activeValue: string
  onClick: (value: string) => void
  cols?: number
}

function RefineSection(props: RefineSectionProps) {
  const { title, titleIcon, refineList, activeValue, onClick, cols } = props
  return (
    <div className="mt-3 first-of-type:mt-6">
      <div className="flex items-center gap-0.5">
        <h3 className="text-xs font-semibold not-italic">{title}</h3>
        {titleIcon ? <img alt="icon" src={titleIcon} /> : null}
      </div>
      <div
        className={clsx(
          'mt-2 grid gap-1',
          cols === 2 && 'grid-cols-2',
          cols === 4 && 'grid-cols-4',
          !cols && 'grid-cols-3',
        )}
      >
        {refineList.map((item) => (
          <AppButton
            className={`font-normal ${cols === 4 ? 'min-w-[60px]' : ''}`}
            cosmeticType={
              activeValue === item.value ? 'tertiary' : 'quaternary'
            }
            key={item.value}
            label={item.label}
            onClick={() => {
              onClick(item.value)
            }}
            style={{ color: item.labelColor }}
          />
        ))}
      </div>
    </div>
  )
}

interface RefineryDatePickerProps {
  label: string
  fromTime: Dayjs | null
  toTime: Dayjs | null
  setFromTime: (date: Dayjs) => void
  setToTime: (date: Dayjs) => void
  setToggle: (mode: string) => void
  toggleMode: string
}

function RefineryDatePicker(props: RefineryDatePickerProps) {
  const {
    fromTime,
    toTime,
    setFromTime,
    setToTime,
    setToggle,
    label,
    toggleMode,
  } = props
  const [dateFilterMode, setDateFilterMode] = useState<'all' | 'from' | 'to'>(
    'all',
  )

  useEffect(() => {
    setDateFilterMode(toggleMode as 'all' | 'from' | 'to')
  }, [toggleMode])

  const fromDateRef = useRef<PickerRef>(null)
  const toDateRef = useRef<PickerRef>(null)

  const toggleDateFilterMode = () => {
    if (dateFilterMode === 'all') {
      setDateFilterMode('from')
      setToggle('from')
    } else if (dateFilterMode === 'from') {
      setDateFilterMode('to')
      setToggle('to')
    } else {
      setDateFilterMode('all')
      setToggle('all')
    }
  }

  const onFocusDateFields = () => {
    if (dateFilterMode === 'from' || dateFilterMode === 'all') {
      fromDateRef.current?.nativeElement.click()
    } else {
      toDateRef.current?.nativeElement.click()
    }
  }

  return (
    <div className="mt-3">
      <h3 className="text-xs font-semibold not-italic">{label}</h3>
      <div className="mt-2 grid grid-cols-[1fr,auto,1fr,auto] items-center gap-2">
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'to'}
          format={{
            format: 'DD-MM-YYYY',
            type: 'mask',
          }}
          maxDate={toTime || undefined}
          onChange={(date: Dayjs) => {
            setFromTime(date)
            if (dateFilterMode === 'all') {
              toDateRef.current?.nativeElement.click()
            }
          }}
          placeholder="DD-MM-YYYY"
          ref={fromDateRef}
          suffixIcon={null}
          value={fromTime}
        />
        <AppButton
          className="rounded-sm"
          cosmeticType="tertiary"
          onClick={toggleDateFilterMode}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={(() => {
                if (dateFilterMode === 'all') {
                  return arrowsHorizontalIcon
                } else if (dateFilterMode === 'from') {
                  return arrowHorizontalLeftIcon
                }
                return arrowHorizontalRightIcon
              })()}
            />
          }
        />
        <DatePicker
          allowClear={false}
          className="w-full rounded-none border-none bg-bg-tone p-3 text-xs font-medium normal-case not-italic placeholder:text-txt-inactive focus-within:bg-bg-tone hover:bg-bg-tone focus:border-0 focus:outline focus:ring-0 disabled:cursor-not-allowed disabled:bg-btn-disabled disabled:text-gray-500 disabled:ring-gray-200"
          disabled={dateFilterMode === 'from'}
          format="DD-MM-YYYY"
          minDate={fromTime || undefined}
          onChange={(date: Dayjs) => {
            const endOfDayTime = date.endOf('day')
            setToTime(endOfDayTime)
          }}
          placeholder="DD-MM-YYYY"
          ref={toDateRef}
          suffixIcon={null}
          value={toTime}
        />
        <AppButton
          className="rounded-sm p-3"
          cosmeticType="quaternary"
          onClick={onFocusDateFields}
          suffixIcon={
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={calendarBlankIcon}
            />
          }
        />
      </div>
    </div>
  )
}

export function RelationshipManagementRefineryDrawer(
  props: RMListRefineryDrawerProps,
) {
  const { isOpened, onClose } = props
  const { listType } = useRecoilValue(relationshipManagementDetailState)

  const { reset } = useForm({
    defaultValues: {},
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: yupResolver(schema),
  })

  const [searchParams, setSearchParams] = useSearchParams()
  const [tempRelationshipManagerStatus, setTempRelationshipManagerStatus] =
    useState(searchParams.get(SEARCH_PARAMS.userStatus) || 'all')
  const [tempRelationshipManagerPosition, setTempRelationshipManagerPosition] =
    useState(searchParams.get(SEARCH_PARAMS.position) || 'all')
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)
  const [createFromTime, setCreatedDateFrom] = useState(
    parseTimeQueryParam(createFromTimeQuery),
  )
  const [createToTime, setCreatedDateTo] = useState(
    parseTimeQueryParam(createToTimeQuery),
  )

  const [createdToggle, setCreatedToggle] = useState('all')

  const onReset = () => {
    reset()
    setTempRelationshipManagerStatus('all')
    setTempRelationshipManagerPosition('all')
    setCreatedToggle('all')
    setCreatedDateFrom(null)
    setCreatedDateTo(null)
  }

  const onApply = () => {
    const updateParam = (param: string, value?: string) => {
      if (value === 'all' || value === 'Invalid Date' || !value) {
        searchParams.delete(param)
      } else {
        searchParams.set(param, value)
      }
    }

    setSearchParams((searchParams) => {
      updateParam(SEARCH_PARAMS.userStatus, tempRelationshipManagerStatus)
      updateParam(SEARCH_PARAMS.position, tempRelationshipManagerPosition)
      updateParam(
        SEARCH_PARAMS.createFromTime,
        String(
          createdToggle === 'to'
            ? 'all'
            : dayjs(createFromTime).valueOf() || 'all',
        ),
      )
      updateParam(
        SEARCH_PARAMS.createToTime,
        String(
          createdToggle === 'from'
            ? 'all'
            : dayjs(createToTime).valueOf() || 'all',
        ),
      )
      return searchParams
    })
    onClose()
  }

  // refresh all queries on open
  useEffect(() => {
    const tempRelationshipManagerStatus = searchParams.get(
      SEARCH_PARAMS.userStatus,
    )
    const tempRelationshipManagerPosition = searchParams.get(
      SEARCH_PARAMS.position,
    )
    setTempRelationshipManagerStatus(tempRelationshipManagerStatus || 'all')
    setTempRelationshipManagerPosition(tempRelationshipManagerPosition || 'all')
    setCreatedDateFrom(parseTimeQueryParam(Number(createFromTimeQuery)))
    setCreatedDateTo(parseTimeQueryParam(Number(createToTimeQuery)))
  }, [isOpened, searchParams])

  return (
    <AppRefineryDrawer
      isOpened={isOpened}
      onApply={onApply}
      onCancel={onClose}
      onClose={onClose}
      onReset={onReset}
    >
      {listType !== 'TEAMS' ? (
        <div>
          <RefineSection
            activeValue={tempRelationshipManagerStatus}
            onClick={(value) => {
              setTempRelationshipManagerStatus(value)
            }}
            refineList={REFINE_MANAGER_STATUS_OPTIONS}
            title="Status"
          />
          <RefineSection
            activeValue={tempRelationshipManagerPosition}
            cols={4}
            onClick={(value) => {
              setTempRelationshipManagerPosition(value)
            }}
            refineList={REFINE_POSOTION_OPTIONS}
            title="Position"
          />
        </div>
      ) : null}
      <RefineryDatePicker
        fromTime={createFromTime}
        label="Created Date"
        setFromTime={setCreatedDateFrom}
        setToTime={setCreatedDateTo}
        setToggle={setCreatedToggle}
        toTime={createToTime}
        toggleMode={createdToggle}
      />
    </AppRefineryDrawer>
  )
}
