import { useRecoilState } from 'recoil'
import { useSearchParams } from 'react-router-dom'
import { RM_LIST_TYPE_OPTIONS } from '@/features/shared/mappings'
import { AppSwitchButton } from '@/features/shared/ui'
import { relationshipManagementDetailState } from '@/features/shared/states'
import { type ListType } from '@/features/shared/services'
import { usePermission } from '@/features/shared/utils'

const VIEW_RM_PERMISSION = 1700050
const VIEW_TEAM_PERMISSION = 1700500

export function RelationshipManagementQuickFilter() {
  const [{ listType }, setRelationshipManagementDetailState] = useRecoilState(
    relationshipManagementDetailState,
  )
  const [, setSearchParams] = useSearchParams()
  const { hasPermission } = usePermission()

  return (
    <div className="flex flex-wrap items-center gap-2">
      <div className="grow">
        <section className="grid grid-cols-[192px_192px_192px] bg-bg-tone">
          {hasPermission(VIEW_RM_PERMISSION) && (
            <AppSwitchButton
              className="h-10"
              isActive={listType === RM_LIST_TYPE_OPTIONS.MANAGERS.key}
              onClick={() => {
                setSearchParams(() => {
                  return {}
                })
                setRelationshipManagementDetailState({
                  listType: RM_LIST_TYPE_OPTIONS.MANAGERS.key as ListType,
                })
              }}
            >
              {RM_LIST_TYPE_OPTIONS.MANAGERS.label}
            </AppSwitchButton>
          )}

          {hasPermission(VIEW_TEAM_PERMISSION) && (
            <AppSwitchButton
              className="h-10"
              isActive={listType === RM_LIST_TYPE_OPTIONS.TEAMS.key}
              onClick={() => {
                setSearchParams(() => {
                  return {}
                })
                setRelationshipManagementDetailState({
                  listType: RM_LIST_TYPE_OPTIONS.TEAMS.key as ListType,
                })
              }}
            >
              {RM_LIST_TYPE_OPTIONS.TEAMS.label}
            </AppSwitchButton>
          )}
        </section>
      </div>
    </div>
  )
}
