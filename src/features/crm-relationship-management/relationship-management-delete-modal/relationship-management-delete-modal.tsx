import { useRecoilState } from 'recoil'
import { relationshipManagementDetailState } from '@/features/shared/states'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/features/shared/utils'
import { AppModalConfirmation } from '@/features/shared/ui'
import {
  type RMDetailActionType,
  CRMRelationshipManagementServices,
} from '@/features/shared/services'

export function RelationshipManagementDeleteModal() {
  const queryClient = useQueryClient()

  const [
    { mode, listType, selectedManager, selectedTeam },
    setRelationshipManagementDetailState,
  ] = useRecoilState(relationshipManagementDetailState)

  const { showToast } = useToast()

  const mutateDeleteRMManagerDetail = useMutation({
    mutationFn: CRMRelationshipManagementServices.deleteRMManagerDetail,
    onError: (error) => {
      showToast('RM Delete Failed', error.message, 'danger')
    },
    onSuccess: () => {
      onCancel()
      queryClient.invalidateQueries({
        queryKey: ['relationship-management-manager-list'],
      })
      showToast(
        'RM Delete',
        `You have deleted a RM - ${selectedManager?.userName}`,
        'success',
      )
    },
  })
  const mutateDeleteRMTeamDetail = useMutation({
    mutationFn: CRMRelationshipManagementServices.deleteRMTeamDetail,
    onError: (error) => {
      showToast('Team Delete Failed', error.message, 'danger')
    },
    onSuccess: () => {
      onCancel()
      queryClient.invalidateQueries({
        queryKey: ['relationship-management-team-list'],
      })

      showToast(
        'Team Delete',
        `You have deleted a team - ${selectedTeam?.teamName}`,
        'success',
      )
    },
  })
  const onConfirm = () => {
    let payload
    if (listType === 'MANAGERS') {
      payload = {
        action: mode! as RMDetailActionType,
        id: selectedManager?.id || 0,
      }
      mutateDeleteRMManagerDetail.mutateAsync(payload)
    } else {
      payload = {
        action: mode! as RMDetailActionType,
        teamId: selectedTeam?.teamId || 0,
      }
      mutateDeleteRMTeamDetail.mutateAsync(payload)
    }
  }
  const onCancel = () => {
    setRelationshipManagementDetailState((prev) => ({
      ...prev,
      mode: undefined,
      selectedManager: undefined,
      selectedTeam: undefined,
    }))
  }

  return (
    <AppModalConfirmation
      confirmBtnText={listType === 'MANAGERS' ? 'Delete RM' : 'Delete Team'}
      isLoading={
        mutateDeleteRMManagerDetail.isPending ||
        mutateDeleteRMTeamDetail.isPending
      }
      isOpened
      modalTitle={listType === 'MANAGERS' ? 'Delete RM' : 'Delete Team'}
      modalWidth={500}
      onCancel={onCancel}
      onConfirm={onConfirm}
      requiresPassword={false}
    >
      <div>
        {listType === 'MANAGERS' ? (
          <p className="mb-1 text-xs font-light leading-4">
            You are about to delete RM -{' '}
            <span className="font-semibold">
              {selectedManager?.userName} {selectedManager?.userId}
            </span>
            .
          </p>
        ) : (
          <p className="mb-1 text-xs font-light leading-4">
            You are about to delete Team -{' '}
            <span className="font-semibold">{selectedTeam?.teamName}</span>.
          </p>
        )}

        <p className="text-xs font-light leading-4">
          This action is irreversible. Would you like to proceed?
        </p>
      </div>
    </AppModalConfirmation>
  )
}
