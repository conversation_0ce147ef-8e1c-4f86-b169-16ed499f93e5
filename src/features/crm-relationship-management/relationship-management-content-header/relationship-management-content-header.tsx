import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
// import { useNavigate, useSearchParams } from 'react-router-dom'
import { useRecoilState } from 'recoil'
import dayjs from 'dayjs'
import { relationshipManagementDetailState } from '@/features/shared/states'
import {
  AppButton,
  AppExportListModal,
  ContentHeader,
} from '@/features/shared/ui'
import { usePermission } from '@/features/shared/utils'
import { plusGreenIcon } from '@/features/shared/ui/app-icons'
import { RM_MANAGER_LIST_SEARCH_PARAMS as SEARCH_PARAMS } from '@/features/shared/services'
import { RM_LIST_TYPE_OPTIONS } from '@/features/shared/mappings'

const ADD_RM_PERMISSION = 1700100
const EXPORT_RM_PERMISSION = 1700400
const ADD_TEAM_PERMISSION = 1700600

export function RelationshipManagementContentHeader() {
  // const navigate = useNavigate()
  const { hasPermission } = usePermission()
  const [searchParams] = useSearchParams()
  const [isExportModalOpened, setIsExportModalOpened] = useState(false)

  const [{ listType }, setRelationshipManagementDetailState] = useRecoilState(
    relationshipManagementDetailState,
  )

  const onBack = () => {
    console.log('back')
    // navigate('/admin-panel/crm/customer-relations/list')
  }

  const openRMModal = () => {
    setRelationshipManagementDetailState((prev) => ({
      ...prev,
      mode: 'ADD',
      selectedManager: undefined,
      selectedTeam: undefined,
    }))
  }

  const onExport = () => {
    setIsExportModalOpened(true)
  }

  const getExportFilters = () => {
    const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
    const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
    const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
    const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
    const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
    const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
    const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
    const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)
    const updateFromTimeQuery = searchParams.get(SEARCH_PARAMS.updateFromTime)
    const updateToTimeQuery = searchParams.get(SEARCH_PARAMS.updateToTime)
    const userStatusQuery = searchParams.get(SEARCH_PARAMS.userStatus)

    return {
      createFromTime: createFromTimeQuery
        ? dayjs(Number(createFromTimeQuery)).valueOf()
        : 0,
      createToTime: createToTimeQuery
        ? dayjs(Number(createToTimeQuery)).valueOf()
        : 0,
      keyword: keywordQuery || '',
      pageNum: Number(pageNumQuery) || 1,
      pageSize: Number(pageSizeQuery) || 10,
      searchType: keywordQuery ? searchTypeQuery || 'USER_ID' : '',
      sortBy: sortByQuery || '',
      sortType: sortTypeQuery || '',
      updateFromTime: updateFromTimeQuery
        ? dayjs(Number(updateFromTimeQuery)).valueOf()
        : 0,
      updateToTime: updateToTimeQuery
        ? dayjs(Number(updateToTimeQuery)).valueOf()
        : 0,
      userStatus: userStatusQuery || '',
    }
  }

  return (
    <>
      <ContentHeader
        onBack={onBack}
        onExport={
          hasPermission(EXPORT_RM_PERMISSION) &&
          listType === RM_LIST_TYPE_OPTIONS.MANAGERS.key
            ? onExport
            : undefined
        }
        title="Relationship Manager"
      >
        {listType === RM_LIST_TYPE_OPTIONS.TEAMS.key &&
          hasPermission(ADD_TEAM_PERMISSION) && (
            <AppButton
              className="mr-2"
              label="Create Team"
              onClick={openRMModal}
              prefixIcon={plusGreenIcon}
            />
          )}

        {listType === RM_LIST_TYPE_OPTIONS.MANAGERS.key &&
          hasPermission(ADD_RM_PERMISSION) && (
            <AppButton
              className="mr-2"
              label="Add RM"
              onClick={openRMModal}
              prefixIcon={plusGreenIcon}
            />
          )}
      </ContentHeader>
      {hasPermission(EXPORT_RM_PERMISSION) && (
        <AppExportListModal
          exportFilters={getExportFilters()}
          isOpen={isExportModalOpened}
          onClose={() => {
            setIsExportModalOpened(false)
          }}
          type="relationshipManager"
        />
      )}
    </>
  )
}
