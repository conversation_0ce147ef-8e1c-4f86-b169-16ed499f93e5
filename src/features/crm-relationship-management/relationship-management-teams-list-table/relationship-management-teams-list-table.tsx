import dayjs from 'dayjs'
import { ReactSVG } from 'react-svg'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { Popover, type TableProps } from 'antd'
import { AppButton, AppTable } from '@/features/shared/ui'
import {
  convertSortTypeToSortOrder,
  usePermission,
} from '@/features/shared/utils'
import {
  CRMRelationshipManagementServices,
  RM_TEAM_LIST_SEARCH_PARAMS as SEARCH_PARAMS,
  type GetRMTeamTableListResponse,
} from '@/features/shared/services'
import {
  dotsThreeVerticalIcon,
  trashIcon,
  editIcon,
  plusIcon,
  memberIcon,
} from '@/features/shared/ui/app-icons'

const ASSIGN_CUSTOMER_PERMISSION = 1600050
const EDIT_TEAM_PERMISSION = 1700700
const DELETE_TEAM_PERMISSION = 1700800

interface RMTeamsListTableProps {
  onRowClick: (
    record: GetRMTeamTableListResponse,
    action: 'EDIT' | 'ASSIGN' | 'DELETE',
  ) => void
}

export function RelationshipManagementTeamsListTable(
  props: RMTeamsListTableProps,
) {
  const { onRowClick } = props
  const { hasPermission } = usePermission()

  const [searchParams] = useSearchParams()
  const keywordQuery = searchParams.get(SEARCH_PARAMS.keyword)
  const searchTypeQuery = searchParams.get(SEARCH_PARAMS.searchType)
  const pageNumQuery = searchParams.get(SEARCH_PARAMS.pageNum)
  const pageSizeQuery = searchParams.get(SEARCH_PARAMS.pageSize)
  const sortByQuery = searchParams.get(SEARCH_PARAMS.sortBy)
  const sortTypeQuery = searchParams.get(SEARCH_PARAMS.sortType)
  const createFromTimeQuery = searchParams.get(SEARCH_PARAMS.createFromTime)
  const createToTimeQuery = searchParams.get(SEARCH_PARAMS.createToTime)
  const updateFromTimeQuery = searchParams.get(SEARCH_PARAMS.updateFromTime)
  const updateToTimeQuery = searchParams.get(SEARCH_PARAMS.updateToTime)

  const { data, isLoading } = useQuery({
    queryFn: () =>
      CRMRelationshipManagementServices.getRMTeamList({
        createFromTime: createFromTimeQuery
          ? dayjs(Number(createFromTimeQuery)).valueOf()
          : 0,
        createToTime: createToTimeQuery
          ? dayjs(Number(createToTimeQuery)).valueOf()
          : 0,
        keyword: keywordQuery || '',
        pageNum: Number(pageNumQuery) || 1,
        pageSize: Number(pageSizeQuery) || 10,
        searchType: keywordQuery ? searchTypeQuery || 'TEAM_ID' : '',
        sortBy: sortByQuery || 'CREATE_TIME',
        sortType: sortTypeQuery || 'DESC',
        updateFromTime: updateFromTimeQuery
          ? dayjs(Number(updateFromTimeQuery)).valueOf()
          : 0,
        updateToTime: updateToTimeQuery
          ? dayjs(Number(updateToTimeQuery)).valueOf()
          : 0,
      }),
    queryKey: [
      'relationship-management-team-list',
      keywordQuery,
      searchTypeQuery,
      pageNumQuery,
      pageSizeQuery,
      sortByQuery,
      sortTypeQuery,
      createFromTimeQuery,
      createToTimeQuery,
      createFromTimeQuery,
      updateFromTimeQuery,
      updateToTimeQuery,
    ],
  })

  const [selectedPopover, setSelectedPopover] = useState<number | null>(null)
  const handleOpenChange = (newOpen: boolean, id: number) => {
    setSelectedPopover(newOpen ? id : null)
  }

  const columns: TableProps<GetRMTeamTableListResponse>['columns'] = [
    {
      dataIndex: 'teamId',
      key: 'TEAM_ID',
      sortOrder:
        sortByQuery === 'TEAM_ID'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Team ID',
    },
    {
      dataIndex: 'teamName',
      key: 'TEAM_NAME',
      sortOrder:
        sortByQuery === 'TEAM_NAME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Team Name',
    },
    {
      dataIndex: 'teamManagerCount',
      key: 'teamManagerCount',
      render: (teamManagerCount: number) => (
        <div className="flex items-center">
          <div className="flex items-center gap-1 rounded-sm bg-bg-default px-1 py-0.5 text-[10px] font-light not-italic leading-[14px] text-txt-paragraph">
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={memberIcon}
            />
            <span>{teamManagerCount}</span>
          </div>
        </div>
      ),
      title: 'Team Leader',
    },
    {
      dataIndex: 'teamMemberCount',
      key: 'teamMemberCount',
      render: (teamMemberCount: number) => (
        <div className="flex items-center">
          <div className="flex items-center gap-1 rounded-sm bg-bg-default px-1 py-0.5 text-[10px] font-light not-italic leading-[14px] text-txt-paragraph">
            <ReactSVG
              beforeInjection={(svg) => {
                svg.classList.add('fill-txt-title')
              }}
              src={memberIcon}
            />
            <span>{teamMemberCount}</span>
          </div>
        </div>
      ),
      title: 'Team Members',
    },
    {
      dataIndex: 'createTime',
      key: 'CREATE_TIME',
      render: (createTime: string) => (
        <span>
          {createTime ? dayjs(createTime).format('DD-MM-YYYY HH:mm') : '--'}
        </span>
      ),
      sortOrder:
        sortByQuery === 'CREATE_TIME'
          ? convertSortTypeToSortOrder(sortTypeQuery)
          : undefined,
      sorter: true,
      title: 'Created Date',
    },
    {
      dataIndex: 'teamId',
      key: 'teamId',
      render: (teamId: number, record) => (
        <div>
          {hasPermission(EDIT_TEAM_PERMISSION) ||
          hasPermission(DELETE_TEAM_PERMISSION) ||
          hasPermission(ASSIGN_CUSTOMER_PERMISSION) ? (
            <Popover
              arrow={false}
              content={
                <div className="flex flex-col">
                  {hasPermission(EDIT_TEAM_PERMISSION) && (
                    <AppButton
                      className="justify-start py-2 font-normal"
                      cosmeticType="transparent"
                      label="Edit/View Team"
                      onClick={(e) => {
                        e.stopPropagation()
                        onRowClick(record, 'EDIT')
                        setSelectedPopover(null)
                      }}
                      prefixIcon={editIcon}
                      type="button"
                    />
                  )}

                  {hasPermission(ASSIGN_CUSTOMER_PERMISSION) && (
                    <AppButton
                      className="justify-start py-2 font-normal"
                      cosmeticType="transparent"
                      label="Assign Customer"
                      onClick={(e) => {
                        e.stopPropagation()
                        onRowClick(record, 'ASSIGN')
                        setSelectedPopover(null)
                      }}
                      prefixIcon={plusIcon}
                      type="button"
                    />
                  )}

                  {hasPermission(DELETE_TEAM_PERMISSION) && (
                    <AppButton
                      className="justify-start py-2 font-normal"
                      cosmeticType="transparent"
                      label="Delete Team"
                      onClick={(e) => {
                        e.stopPropagation()
                        onRowClick(record, 'DELETE')
                        setSelectedPopover(null)
                      }}
                      prefixIcon={trashIcon}
                      type="button"
                    />
                  )}
                </div>
              }
              onOpenChange={(newOpen) => {
                handleOpenChange(newOpen, teamId)
              }}
              open={selectedPopover === teamId}
              placement="bottomRight"
              trigger="click"
            >
              <AppButton
                cosmeticType="transparent"
                onClick={(e) => {
                  e.stopPropagation()
                }}
                prefixIcon={
                  <ReactSVG
                    beforeInjection={(svg) => {
                      svg.classList.add('fill-txt-title')
                    }}
                    src={dotsThreeVerticalIcon}
                  />
                }
                type="button"
              />
            </Popover>
          ) : null}
        </div>
      ),
      title: ' ',
      width: 40,
    },
  ]

  return (
    <AppTable
      columns={columns}
      dataSource={data?.list || []}
      isPaginationShown
      loading={isLoading}
      rowKey="teamId"
      total={data?.total}
    />
  )
}
