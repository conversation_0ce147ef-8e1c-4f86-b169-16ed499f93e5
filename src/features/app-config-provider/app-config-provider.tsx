import type { ReactNode } from 'react'
import { useRecoilValue } from 'recoil'
import { ConfigProvider, theme } from 'antd'
import { NoData } from '@/features/shared/ui'
import { uiState } from '@/features/shared/states'

interface AppConfigProviderProps {
  children: ReactNode
}
export function AppConfigProvider(props: AppConfigProviderProps) {
  const { theme: appTheme } = useRecoilValue(uiState)
  const { children } = props
  return (
    <ConfigProvider
      renderEmpty={() => <NoData />}
      theme={{
        algorithm: [
          appTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
          theme.compactAlgorithm,
        ],
        components: {
          Table: {
            rowSelectedBg: 'rgb(var(--color-btn-3rd))',
            rowSelectedHoverBg: 'rgb(var(--color-btn-3rd))',
          },
        },
        token: {
          fontFamily: 'Noto Sans, sans-serif',
        },
      }}
    >
      {children}
    </ConfigProvider>
  )
}
