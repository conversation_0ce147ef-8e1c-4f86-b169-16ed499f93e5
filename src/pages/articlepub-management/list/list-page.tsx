import { useState } from 'react'
import { useRecoilState } from 'recoil'
import {
  ArticlepubBreadcrumb,
  ArticlepubContentHeader,
  ArticlepubRefineryAndSearch,
  ArticlepubRefineryDrawer,
  ArticlepubListTable,
  ArticlepubAddEditModal,
  ArticlepubDeleteModal,
  ArticlepubPublishModal,
  ArticlepubViewModal,
} from '@/features/articlepub-management'
import { MeritDivider } from '@/features/shared/ui'
import { RefinedFilterTags } from '@/features/shared/ui'
import { articlepubDetailState } from '@/features/shared/states'
import { ArticlepubActionType, ArticlePubSearchParamsLabels, GetArticlepubListResponse } from '@/features/shared/services'

export default function ArticlepubListPage() {
  const [isRefineryDrawerOpened, setIsRefineryDrawerOpened] = useState(false)
  const [{ mode }, setArticlepubDetailState] = useRecoilState(
    articlepubDetailState,
  )

  const isAddEditModalOpened = mode === 'ADD' || mode === 'EDIT'
  const isDeleteModalOpened = mode === 'DELETE'
  const isPublishModalOpened = mode === 'PUBLISH'
  const isViewModalOpened = mode === 'VIEW'

  const onRowClick = (
    record: GetArticlepubListResponse,
    action: 'VIEW' | 'EDIT' | 'DELETE' | 'PUBLISH',
  ) => {
    switch (action) {
      case 'VIEW':
      case 'EDIT':
      case 'DELETE':
      case 'PUBLISH':
        setArticlepubDetailState((prev) => ({
          ...prev,
          mode: action,
          selectedArticlepub: {
            ...record,
            action: action as ArticlepubActionType,
          },
        }))
        break
      default:
        break
    }
  }

  return (
    <div className="relative h-full overflow-x-hidden">
      <section className="flex flex-col gap-3 p-6">
        <ArticlepubBreadcrumb />
        <ArticlepubContentHeader />
        <MeritDivider isList />
        <ArticlepubRefineryAndSearch
          onRefineListClick={() => {
            setIsRefineryDrawerOpened(true)
          }}
        />
        <RefinedFilterTags criteriaLabels={ArticlePubSearchParamsLabels} />
        <ArticlepubRefineryDrawer
          isOpened={isRefineryDrawerOpened}
          onClose={() => {
            setIsRefineryDrawerOpened(false)
          }}
        />
        <ArticlepubListTable onRowClick={onRowClick} />
      </section>

      {isAddEditModalOpened && <ArticlepubAddEditModal />}
      {isDeleteModalOpened && <ArticlepubDeleteModal />}
      {isPublishModalOpened && <ArticlepubPublishModal />}
      {isViewModalOpened && <ArticlepubViewModal />}
    </div>
  )
}