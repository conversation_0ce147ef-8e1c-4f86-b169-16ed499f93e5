import { useState } from 'react'
import { useRecoilState } from 'recoil'
import {
  MessageCenterBreadcrumb,
  MessageCenterContentHeader,
  MessageCenterRefineryAndSearch,
  MessageCenterRefineryDrawer,
  MessageCenterListTable,
  MessageCenterAddEditModal,
  MessageCenterDeleteModal,
  MessageCenterPublishModal,
  MessageCenterViewModal,
} from '@/features/crm-message-center'
import { MeritDivider } from '@/features/shared/ui'
import { RefinedFilterTags } from '@/features/shared/ui'
import {
  MessageCenterSearchParamsLabels,
  type GetMessageListResponse,
} from '@/features/shared/services'
import { messageCenterDetailState } from '@/features/shared/states'

export default function MessageCenterListPage() {
  const [isRefineryDrawerOpened, setIsRefineryDrawerOpened] = useState(false)
  const [{ mode }, setMessageCenterDetailState] = useRecoilState(
    messageCenterDetailState,
  )

  const isAddEditModalOpened = mode === 'ADD' || mode === 'EDIT'
  const isDeleteModalOpened = mode === 'DELETE'
  const isPublishModalOpened = mode === 'PUBLISH'
  const isViewModalOpened = mode === 'VIEW'

  const onRowClick = (
    record: GetMessageListResponse,
    action: 'VIEW' | 'EDIT' | 'DELETE' | 'PUBLISH',
  ) => {
    switch (action) {
      case 'VIEW':
      case 'EDIT':
      case 'DELETE':
      case 'PUBLISH':
        setMessageCenterDetailState((prev) => ({
          ...prev,
          mode: action,
          selectedMessage: record,
        }))
        break
      default:
        break
    }
  }

  return (
    <div className="relative h-full overflow-x-hidden">
      <section className="flex flex-col gap-3 p-6">
        <MessageCenterBreadcrumb />
        <MessageCenterContentHeader />
        <MeritDivider isList />
        <MessageCenterRefineryAndSearch
          onRefineListClick={() => {
            setIsRefineryDrawerOpened(true)
          }}
        />
        <RefinedFilterTags criteriaLabels={MessageCenterSearchParamsLabels} />
        <MessageCenterRefineryDrawer
          isOpened={isRefineryDrawerOpened}
          onClose={() => {
            setIsRefineryDrawerOpened(false)
          }}
        />
        <MessageCenterListTable onRowClick={onRowClick} />
      </section>

      {isAddEditModalOpened && <MessageCenterAddEditModal />}
      {isDeleteModalOpened && <MessageCenterDeleteModal />}
      {isPublishModalOpened && <MessageCenterPublishModal />}
      {isViewModalOpened && <MessageCenterViewModal />}
    </div>
  )
}