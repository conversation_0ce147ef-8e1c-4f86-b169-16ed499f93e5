
pipeline {
    // 添加参数定义
    parameters {
        choice(
            name: 'BRANCH_NAME',
            choices: ['develop', 'main'],
            description: '构建分支：develop, main, develop 对应开发分支，main 对应发布分支'
        )
        string(
            name: 'VERSION_PREFIX',
            defaultValue: '1.0',
            description: '版本号前缀（如：1.0, 2.0）'
        )
    }
    
    environment {
        projectName = "merit-web"
        repositoryUrl = "http://**************:8080/merit/frontend/merit-web.git"
        registry = "hub.skymind.io/merit-bo/"
        BUILD_TYPE = "${params.BRANCH_NAME == "develop" ? "develop" : "release"}"
        // 根据构建类型和 Jenkins 构建号生成版本号
        IMAGE_VERSION = "${params.VERSION_PREFIX}.${BUILD_NUMBER}-${BUILD_TYPE}"
        // 完整的镜像名称
        FULL_IMAGE_NAME = "${registry}${projectName}:${IMAGE_VERSION}"
    }
    
    agent any
    
    stages {

        stage('Print Build Info') {
            steps {
                echo "构建信息："
                echo "- 项目名称: ${projectName}"
                echo "- Git地址: ${repositoryUrl}"
                echo "- 构建分支: ${params.BRANCH_NAME}"
                echo "- 构建类型: ${BUILD_TYPE}"
                echo "- 版本号: ${IMAGE_VERSION}"
                echo "- 完整镜像名: ${FULL_IMAGE_NAME}"
                echo "- Jenkins 构建号: ${BUILD_NUMBER}"
            }
        }
        
        stage('Build image') {
            steps {
                echo 'Building Docker image....'
                script {
                    // 直接使用 docker 命令构建镜像
                    sh "docker build -t ${FULL_IMAGE_NAME} ."
                }
            }
        }
        
        stage('Push image') {
            steps {
                echo 'Pushing image to Harbor registry....'
                script {
                    // 直接使用 docker 命令推送镜像
                    sh "docker push ${FULL_IMAGE_NAME}"
                }
            }
        }
    }
    
    post {
        success {
            echo "✅ 构建成功！"
            echo "📦 镜像已推送: ${FULL_IMAGE_NAME}"
        }
        failure {
            echo "❌ 构建失败！"
        }
        always {
            echo "🏁 构建完成"
        }
    }
}
