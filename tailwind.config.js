/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'bg-default': 'rgb(var(--color-bg-default) / <alpha-value>)',
        'bg-pale': 'rgb(var(--color-bg-pale) / <alpha-value>)',
        'bg-tone': 'rgb(var(--color-bg-tone) / <alpha-value>)',
        'bg-accent': 'rgb(var(--color-bg-accent) / <alpha-value>)',
        'bg-disabled': 'rgb(var(--color-bg-disabled) / <alpha-value>)',
        'bg-caution': 'rgb(var(--color-bg-caution) / <alpha-value>)',
        'bg-positive': 'rgb(var(--color-bg-positive) / <alpha-value>)',
        'bg-negative': 'rgb(var(--color-bg-negative) / <alpha-value>)',
        'bg-info': 'rgb(var(--color-bg-info) / <alpha-value>)',

        'btn-primary': 'rgb(var(--color-btn-primary) / <alpha-value>)',
        'btn-2nd': 'rgb(var(--color-btn-2nd) / <alpha-value>)',
        'btn-3rd': 'rgb(var(--color-btn-3rd) / <alpha-value>)',
        'btn-4th': 'rgb(var(--color-btn-4th) / <alpha-value>)',
        'btn-caution': 'rgb(var(--color-btn-caution) / <alpha-value>)',
        'btn-positive': 'rgb(var(--color-btn-positive) / <alpha-value>)',
        'btn-negative': 'rgb(var(--color-btn-negative) / <alpha-value>)',
        'btn-info': 'rgb(var(--color-btn-info) / <alpha-value>)',
        'btn-disabled': 'rgb(var(--color-btn-disabled) / <alpha-value>)',

        'txt-paragraph': 'rgb(var(--color-txt-paragraph) / <alpha-value>)',
        'txt-title': 'rgb(var(--color-txt-title) / <alpha-value>)',
        'txt-label': 'rgb(var(--color-txt-label) / <alpha-value>)',
        'txt-caution': 'rgb(var(--color-txt-caution) / <alpha-value>)',
        'txt-positive': 'rgb(var(--color-txt-positive) / <alpha-value>)',
        'txt-negative': 'rgb(var(--color-txt-negative) / <alpha-value>)',
        'txt-info': 'rgb(var(--color-txt-info) / <alpha-value>)',
        'txt-disable': 'rgb(var(--color-txt-disable) / <alpha-value>)',
        'txt-inactive': 'rgb(var(--color-txt-inactive) / <alpha-value>)',
        'txt-inverted': 'rgb(var(--color-txt-inverted) / <alpha-value>)',

        'chart-emerald-green':
          'rgb(var(--color-chart-emerald-green) / <alpha-value>)',
        'chart-purple': 'rgb(var(--color-chart-purple) / <alpha-value>)',
        'chart-orange': 'rgb(var(--color-chart-orange) / <alpha-value>)',
        'chart-blue': 'rgb(var(--color-chart-blue) / <alpha-value>)',
        'chart-red': 'rgb(var(--color-chart-red) / <alpha-value>)',
        'chart-green': 'rgb(var(--color-chart-green) / <alpha-value>)',

        'bg-risk-level-low':
          'rgb(var(--color-risk-level-bg-low) / <alpha-value>)',
        'bg-risk-level-medium':
          'rgb(var(--color-risk-level-bg-medium) / <alpha-value>)',
        'bg-risk-level-high':
          'rgb(var(--color-risk-level-bg-high) / <alpha-value>)',
        'txt-risk-level-low':
          'rgb(var(--color-risk-level-txt-low) / <alpha-value>)',
        'txt-risk-level-medium':
          'rgb(var(--color-risk-level-txt-medium) / <alpha-value>)',
        'txt-risk-level-high':
          'rgb(var(--color-risk-level-txt-high) / <alpha-value>)',
      },
    },
    fontFamily: {
      sans: ['Noto Sans', 'sans-serif'],
    },
  },
  variants: {
    extend: {
      fill: ['group-hover', 'hover'],
    },
  },
  plugins: [require('@tailwindcss/typography'), require('@tailwindcss/forms')],
}
